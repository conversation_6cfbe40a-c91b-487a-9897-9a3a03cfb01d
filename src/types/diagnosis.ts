export interface DiagnosisModule {
  id: string;
  name: string;
  description: string;
  difyConfig: {
    apiKey: string;
    endpoint: string;
    workflowId?: string;
    agentId?: string;
  };
}

export interface DiagnosticResult {
  id: string;
  workflow_id: string;
  sequence_number: number;
  status: string;
  outputs: {
    text: string;
  };
  error: string | null;
  elapsed_time: number;
  total_tokens: number;
  total_steps: number;
  created_by: {
    id: string;
    user: string;
  };
  created_at: number;
  finished_at: number;
  exceptions_count: number;
  files: any[];
}

export interface DifyWorkflowRequest {
  inputs: Record<string, any>;
  response_mode: 'streaming' | 'blocking';
  user: string;
}

export interface DifyWorkflowResponse {
  workflow_run_id: string;
  task_id: string;
  data: DiagnosticResult;
}
