import request from '@/utils/request';
import { DiagnosisModule } from '../types/diagnosis';

// 默认配置文件路径
const DEFAULT_CONFIG_PATH = '/defaults/diagnosis-modules.json';

// 从配置文件获取模块列表
export const getDiagnosisModules = async (): Promise<DiagnosisModule[]> => {
  try {
    const response = await fetch(`${DEFAULT_CONFIG_PATH}?t=${new Date().getTime()}`);
    if (!response.ok) {
      console.error('Failed to load diagnosis modules from file');
      return [];
    }

    return await response.json();
  } catch (error) {
    console.error('Error loading diagnosis modules:', error);
    return [];
  }
};

// 创建系统诊断
export const createDiagnosisModule = async (module: Omit<DiagnosisModule, 'id'>): Promise<DiagnosisModule> => {
  const newModule = {
    ...module,
    id: `module-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
  };

  // 这里只是模拟创建，实际需要更新配置文件
  console.log('Created module:', newModule);
  return newModule;
};

// 更新系统诊断
export const updateDiagnosisModule = async (id: string, module: Partial<DiagnosisModule>): Promise<DiagnosisModule> => {
  // 这里只是模拟更新，实际需要更新配置文件
  const updatedModule = { ...module, id } as DiagnosisModule;
  console.log('Updated module:', updatedModule);
  return updatedModule;
};

// 删除系统诊断
export const deleteDiagnosisModule = async (id: string): Promise<void> => {
  // 这里只是模拟删除，实际需要更新配置文件
  console.log('Deleted module:', id);
};

// 导出配置文件
export const exportDiagnosisModulesFile = (modules: DiagnosisModule[]) => {
  const dataStr = JSON.stringify(modules, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });
  const url = URL.createObjectURL(dataBlob);
  const link = document.createElement('a');
  link.href = url;
  link.download = 'diagnosis-modules.json';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

// 直接调用 Dify Workflow API
export const runDiagnosis = async (module: DiagnosisModule) => {
  try {
    // 确保 difyConfig 是对象而不是字符串
    const difyConfig = typeof module.difyConfig === 'string'
      ? JSON.parse(module.difyConfig)
      : module.difyConfig;

    console.log('Parsed Dify Config:', difyConfig); // 调试日志

    // 添加配置验证
    if (!difyConfig) {
      throw new Error('Dify configuration is missing');
    }

    if (!difyConfig.apiKey) {
      throw new Error('API Key is required');
    }

    if (!difyConfig.workflowId) {
      throw new Error('Workflow ID is required');
    }

    if (!difyConfig.endpoint) {
      throw new Error('Endpoint URL is required');
    }

    const endpoint = `${difyConfig.endpoint}/workflows/run`;

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${difyConfig.apiKey}`,
      },
      body: JSON.stringify({
        workflow_id: difyConfig.workflowId,
        inputs: {},
        response_mode: "streaming",
        user: "default-user"
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `Dify API error: ${response.statusText}`);
    }

    // 处理流式响应
    if (response.headers.get('content-type')?.includes('text/event-stream')) {
      return handleStreamResponse(response);
    }

    // 处理普通响应
    return await response.json();
  } catch (error) {
    console.error('Diagnosis execution failed:', error);
    throw error;
  }
};

// 处理 SSE 流式响应
const handleStreamResponse = async (response: Response) => {
  const reader = response.body?.getReader();
  if (!reader) {
    throw new Error('Unable to read response stream');
  }

  const decoder = new TextDecoder();
  let buffer = '';

  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6));
            // 根据不同的事件类型处理响应
            switch (data.event) {
              case 'workflow_started':
                console.log('Workflow started:', data);
                break;
              case 'workflow_finished':
                console.log('Workflow finished:', data);
                return data;
              case 'error':
                throw new Error(data.message || 'Unknown error');
              default:
                console.log('Event:', data);
            }
          } catch (e) {
            console.error('Failed to parse stream data:', e);
          }
        }
      }
    }
  } finally {
    reader.releaseLock();
  }
};
