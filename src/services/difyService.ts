import { DiagnosisModule, DifyWorkflowRequest, DifyWorkflowResponse, DiagnosticResult } from '@/types/diagnosis';

export class DifyService {
  private apiKey: string;
  private endpoint: string;
  private workflowId?: string;

  constructor(config: DiagnosisModule['difyConfig']) {
    this.apiKey = config.apiKey;
    this.endpoint = config.endpoint;
    this.workflowId = config.workflowId;
  }

  /**
   * 执行工作流
   */
  async runWorkflow(inputs: Record<string, any>, user: string = 'system'): Promise<DiagnosticResult> {
    if (!this.workflowId) {
      throw new Error('Workflow ID is required');
    }

    const request: DifyWorkflowRequest = {
      inputs,
      response_mode: 'blocking',
      user,
    };

    try {
      const response = await fetch(`${this.endpoint}/workflows/run`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Dify API error: ${response.status} - ${errorText}`);
      }

      const result: DifyWorkflowResponse = await response.json();
      return result.data;
    } catch (error) {
      console.error('Dify workflow execution failed:', error);
      throw error;
    }
  }

  /**
   * 获取工作流运行状态
   */
  async getWorkflowRunStatus(workflowRunId: string): Promise<DiagnosticResult> {
    try {
      const response = await fetch(`${this.endpoint}/workflows/runs/${workflowRunId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Dify API error: ${response.status} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to get workflow run status:', error);
      throw error;
    }
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${this.endpoint}/parameters`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      return response.ok;
    } catch (error) {
      console.error('Dify connection test failed:', error);
      return false;
    }
  }
}

/**
 * 诊断模块管理器
 */
export class DiagnosisModuleManager {
  private modules: DiagnosisModule[] = [];

  constructor() {
    this.loadDefaultModules();
  }

  /**
   * 加载默认模块
   */
  private async loadDefaultModules() {
    try {
      const response = await fetch('/defaults/diagnosis-modules.json');
      if (response.ok) {
        this.modules = await response.json();
      }
    } catch (error) {
      console.error('Failed to load default diagnosis modules:', error);
    }
  }

  /**
   * 获取所有模块
   */
  getModules(): DiagnosisModule[] {
    return this.modules;
  }

  /**
   * 根据ID获取模块
   */
  getModuleById(id: string): DiagnosisModule | undefined {
    return this.modules.find(module => module.id === id);
  }

  /**
   * 添加模块
   */
  addModule(module: DiagnosisModule) {
    this.modules.push(module);
  }

  /**
   * 更新模块
   */
  updateModule(id: string, updates: Partial<DiagnosisModule>) {
    const index = this.modules.findIndex(module => module.id === id);
    if (index !== -1) {
      this.modules[index] = { ...this.modules[index], ...updates };
    }
  }

  /**
   * 删除模块
   */
  removeModule(id: string) {
    this.modules = this.modules.filter(module => module.id !== id);
  }

  /**
   * 创建Dify服务实例
   */
  createDifyService(moduleId: string): DifyService {
    const module = this.getModuleById(moduleId);
    if (!module) {
      throw new Error(`Module with ID ${moduleId} not found`);
    }
    return new DifyService(module.difyConfig);
  }
}

// 单例实例
export const diagnosisModuleManager = new DiagnosisModuleManager();
