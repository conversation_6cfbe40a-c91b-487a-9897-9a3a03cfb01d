import api from '@/utils/api';

// 获取应用列表
export const getApplications = () => {
  return fetch(`${api.applications}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('API响应原始数据:', data);
    return data;
  })
  .catch(error => {
    console.error('API请求异常:', error);
    console.error('错误详情:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    throw error;
  });
};

// 创建应用
export const createApplication = (data: any) => {
  return fetch(`${api.applications}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    body: JSON.stringify(data)
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('创建应用API响应:', data);
    return data;
  })
  .catch(error => {
    console.error('创建应用API异常:', error);
    throw error;
  });
};

// 删除应用
export const deleteApplication = (id: number) => {
  return fetch(`${api.applications}/${id}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('删除应用API响应:', data);
    return data;
  })
  .catch(error => {
    console.error('删除应用API异常:', error);
    throw error;
  });
};

// 更新应用
export const updateApplication = (id: number, data: any) => {
  return fetch(`${api.applications}/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    body: JSON.stringify(data)
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('更新应用API响应:', data);
    return data;
  })
  .catch(error => {
    console.error('更新应用API异常:', error);
    throw error;
  });
};

// 测试Dify应用连通性
// 测试Dify应用连通性
export const testDifyConnection = (apiKey: string, apiUrl: string) => {
  // 确保URL以/结尾
  const baseUrl = apiUrl.endsWith('/') ? apiUrl : `${apiUrl}/`;
  // 访问info接口
  const infoUrl = `${baseUrl}info`;
  
  console.log('开始测试Dify连接:', infoUrl);
  
  return fetch(infoUrl, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    // 不使用no-cors模式，因为我们需要读取响应内容
    mode: 'cors'
  })
  .then(response => {
    console.log('Dify连接测试状态码:', response.status);
    
    // 检查HTTP状态码
    if (response.status === 200) {
      return response.json().then(data => {
        console.log('Dify连接测试响应数据:', data);
        
        // 检查返回的JSON数据中是否包含name字段
        if (data && data.name) {
          return {
            success: true,
            message: `连接成功，服务名称: ${data.name}`,
            data: data
          };
        } else {
          return {
            success: false,
            error: '连接成功但返回数据格式不符合预期（缺少name字段）'
          };
        }
      });
    } else {
      return {
        success: false,
        error: `HTTP状态码错误: ${response.status}`
      };
    }
  })
  .catch(error => {
    console.error('测试Dify连通性异常:', error);
    return {
      success: false,
      error: error.message || '连接失败'
    };
  });
};