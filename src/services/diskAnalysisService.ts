import api from '@/utils/api';

// 数据库概览接口
export const getDatabaseOverview = () => {
  return fetch(`${api.diskAnalysis}/mysql/overview`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('数据库概览API响应:', data);
    return data;
  })
  .catch(error => {
    console.error('数据库概览API异常:', error);
    console.error('错误详情:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    throw error;
  });
};

// 数据库列表接口
export const getDatabaseList = (params?: {
  search?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}) => {
  const queryParams = new URLSearchParams();
  
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, String(value));
      }
    });
  }
  
  const queryString = queryParams.toString();
  const url = `${api.diskAnalysis}/mysql/databases${queryString ? `?${queryString}` : ''}`;
  
  return fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('数据库列表API响应:', data);
    return data;
  })
  .catch(error => {
    console.error('数据库列表API异常:', error);
    console.error('错误详情:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    throw error;
  });
};

// 大文件列表接口
export const getLargeFilesList = (params?: {
  search?: string;
  page?: number;
  pageSize?: number;
  minSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}) => {
  const queryParams = new URLSearchParams();
  
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, String(value));
      }
    });
  }
  
  const queryString = queryParams.toString();
  const url = `${api.diskAnalysis}/mysql/large-files${queryString ? `?${queryString}` : ''}`;
  
  return fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('大文件列表API响应:', data);
    return data;
  })
  .catch(error => {
    console.error('大文件列表API异常:', error);
    console.error('错误详情:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    throw error;
  });
};

// 趋势数据接口（备用，点击按钮时调用）
export const getTrendData = (params: {
  databaseId?: number;
  timeRange: '7d' | '14d' | '30d';
  type: 'total' | 'daily';
}) => {
  const queryParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, String(value));
    }
  });
  
  const queryString = queryParams.toString();
  const url = `${api.diskAnalysis}/mysql/trend${queryString ? `?${queryString}` : ''}`;
  
  return fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('趋势数据API响应:', data);
    return data;
  })
  .catch(error => {
    console.error('趋势数据API异常:', error);
    console.error('错误详情:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    throw error;
  });
};

// 刷新数据接口（用于刷新按钮）
export const refreshDiskAnalysisData = () => {
  return fetch(`${api.diskAnalysis}/mysql/refresh`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('刷新数据API响应:', data);
    return data;
  })
  .catch(error => {
    console.error('刷新数据API异常:', error);
    console.error('错误详情:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    throw error;
  });
};

// 数据库概览趋势数据接口（简化版）
export const getDatabaseOverviewTrendData = (params: {
  timeRange: '7d' | '14d' | '30d';
}) => {
  const queryParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, String(value));
    }
  });
  
  const queryString = queryParams.toString();
  const url = `${api.diskAnalysis}/mysql/overview/trend${queryString ? `?${queryString}` : ''}`;
  
  return fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('数据库概览趋势数据API响应:', data);
    return data;
  })
  .catch(error => {
    console.error('数据库概览趋势数据API异常:', error);
    throw error;
  });
};

// 单个数据库趋势数据接口
export const getDatabaseTrendData = (params: {
  databaseName: string;
  timeRange: '7d' | '14d' | '30d';
}) => {
  const queryParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, String(value));
    }
  });
  
  const queryString = queryParams.toString();
  const url = `${api.diskAnalysis}/mysql/database/trend${queryString ? `?${queryString}` : ''}`;
  
  return fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('单个数据库趋势数据API响应:', data);
    return data;
  })
  .catch(error => {
    console.error('单个数据库趋势数据API异常:', error);
    throw error;
  });
};

// 大文件趋势数据接口
export const getLargeFileTrendData = (params: {
  fileName: string;
  filePath: string;
  timeRange: '7d' | '14d' | '30d';
}) => {
  const queryParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, String(value));
    }
  });
  
  const queryString = queryParams.toString();
  const url = `${api.diskAnalysis}/mysql/largefile/trend${queryString ? `?${queryString}` : ''}`;
  
  return fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('大文件趋势数据API响应:', data);
    return data;
  })
  .catch(error => {
    console.error('大文件趋势数据API异常:', error);
    throw error;
  });
};

// 获取数据库表大小分布数据
export const getDatabaseTableSizeDistribution = (params: {
  databaseName: string;
  search?: string;
  sortBy?: 'size' | 'rows' | 'tableName';
  sortOrder?: 'asc' | 'desc';
}) => {
  const queryParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, String(value));
    }
  });
  
  const queryString = queryParams.toString();
  const url = `${api.diskAnalysis}/mysql/database/tables${queryString ? `?${queryString}` : ''}`;
  
  return fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('数据库表大小分布API响应:', data);
    return data;
  })
  .catch(error => {
    console.error('数据库表大小分布API异常:', error);
    throw error;
  });
};

// 数据库表趋势数据接口
export const getDatabaseTableTrendData = (params: {
  databaseName: string;
  tableName: string;
  timeRange: '7d' | '14d' | '30d';
}) => {
  const queryParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, String(value));
    }
  });
  
  const queryString = queryParams.toString();
  const url = `${api.diskAnalysis}/mysql/database/tables/trend${queryString ? `?${queryString}` : ''}`;
  
  return fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('数据库表趋势数据API响应:', data);
    return data;
  })
  .catch(error => {
    console.error('数据库表趋势数据API异常:', error);
    throw error;
  });
};