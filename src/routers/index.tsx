/*
 * Copyright 2022 Nightingale Team
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
import React from 'react';
import { Switch, Route, useLocation, Redirect } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import querystring from 'query-string';
import { RootState, accountStoreState } from '@/store/accountInterface';
import NotFound from '@/pages/NotFound';
import Page403 from '@/pages/NotFound/Page403';
import Login from '@/pages/login';
import Overview from '@/pages/login/overview';
import LoginCallback from '@/pages/loginCallback';
import Strategy from '@/pages/warning/strategy';
import Profile from '@/pages/account/profile';
import Dashboard from '@/pages/dashboard/List';
import Chart from '@/pages/chart';
import DashboardDetail from '@/pages/dashboard/Detail/index';
import DashboardShare from '@/pages/dashboard/Share/index';
import Groups from '@/pages/user/groups';
import Users from '@/pages/user/users';
import Business from '@/pages/user/business';
import Explore from '@/pages/metric/explorer';
import ObjectExplore from '@/pages/monitor/object';
import IndicatorPage from '@/pages/monitor/indicator';
import StrategyAdd from '@/pages/warning/strategy/add';
import StrategyEdit from '@/pages/warning/strategy/edit';
import StrategyBrain from '@/pages/warning/strategy/Jobs';
import Shield from '@/pages/warning/shield';
import AddShield from '@/pages/warning/shield/add';
import ShieldEdit from '@/pages/warning/shield/edit';
import Subscribe from '@/pages/warning/subscribe';
import SubscribeAdd from '@/pages/warning/subscribe/add';
import SubscribeEdit from '@/pages/warning/subscribe/edit';
import Event from '@/pages/event';
import EventDetail from '@/pages/event/detail';
import historyEvents from '@/pages/historyEvents';
import MonObjectManage from '@/pages/monObjectManage';
import Demo from '@/pages/demo';
import TaskTpl from '@/pages/taskTpl';
import TaskTplAdd from '@/pages/taskTpl/add';
import TaskTplDetail from '@/pages/taskTpl/detail';
import TaskTplModify from '@/pages/taskTpl/modify';
import TaskTplClone from '@/pages/taskTpl/clone';
import Task from '@/pages/task';
import TaskAdd from '@/pages/task/add';
import TaskResult from '@/pages/task/result';
import TaskDetail from '@/pages/task/detail';
import Version from '@/pages/help/version';
import Contact from '@/pages/help/contact';
import Migrate from '@/pages/help/migrate';
import Servers from '@/pages/help/servers';
import Datasource from '@/pages/datasource';
import DatasourceAdd from '@/pages/datasource/Form';
import RecordingRule from '@/pages/recordingRules';
import RecordingRuleAdd from '@/pages/recordingRules/add';
import RecordingRuleEdit from '@/pages/recordingRules/edit';
import { dynamicPackages, Entry } from '@/utils';

import MQManagement from '@/pages/mq';
import DiskAnalysisPage from '@/pages/disk-analysis';
import DatabaseDetailPage from '@/pages/disk-analysis/components/DatabaseDetailPage';
import AppManagement from '@/pages/appManagement';
import AiDiagnosis from '@/pages/aiDiagnosis';

const Packages = dynamicPackages();
let lazyRoutes = Packages.reduce((result: any, module: Entry) => {
  return (result = result.concat(module.routes));
}, []);

function RouteWithSubRoutes(route) {
  return (
    <Route
      path={route.path}
      render={(props) => (
        // pass the sub-routes down to keep nesting
        <route.component {...props} routes={route.routes} />
      )}
    />
  );
}

export default function Content() {
  let { profile } = useSelector<RootState, accountStoreState>((state) => state.account);
  const location = useLocation();
  const dispatch = useDispatch();
  if (!profile.id && location.pathname != '/monitor/login' && !location.pathname.startsWith('/monitor/callback')) {
    dispatch({ type: 'common/getClusters' });
    if (
      !location.pathname.startsWith('/monitor/chart/') &&
      !location.pathname.startsWith('/monitor/dashboards/share/') &&
      !location.pathname.startsWith('/monitor/alert-cur-events/') &&
      !location.pathname.startsWith('/monitor/alert-his-events/') &&
      !location.pathname.startsWith('/monitor/callback')
    ) {
      dispatch({ type: 'account/getProfile' });
      dispatch({ type: 'common/getBusiGroups' });
    }
  }

  // 大盘在全屏和暗黑主题下需要定义个 dark 样式名
  let themeClassName = '';
  if (location.pathname.indexOf('/dashboard') === 0) {
    const query = querystring.parse(location.search);
    if (query?.viewMode === 'fullscreen' && query?.themeMode === 'dark') {
      themeClassName = 'theme-dark';
    }
  }

  return (
    <div className={`content ${themeClassName}`}>
      <Switch>
        <Route path='/monitor/demo' component={Demo} />
        <Route path='/monitor/overview' component={Overview} />
        <Route path='/monitor/login' component={Login} exact />
        <Route path='/monitor/callback' component={LoginCallback} exact />
        <Route path='/monitor/metric/explorer' component={Explore} exact />
        <Route path='/monitor/object/explorer' component={ObjectExplore} exact />
        <Route path='/monitor/busi-groups' component={Business} />
        <Route path='/monitor/users' component={Users} />
        <Route path='/monitor/user-groups' component={Groups} />
        <Route path='/monitor/account/profile/:tab' component={Profile} />
        
        {/* 添加AI系统诊断路由 */}
        <Route path='/monitor/ai-diagnosis' component={AiDiagnosis} exact />
        {/* 添加MQ管理路由 */}
        <Route path='/monitor/mq' component={MQManagement} />
        {/* 添加容量分析路由 */}
        <Route path='/monitor/disk-analysis' component={DiskAnalysisPage} exact />
        {/* 添加数据库详情页面路由 */}
        <Route path='/monitor/disk-analysis/database-detail' component={DatabaseDetailPage} exact />
        {/* 添加应用管理路由 */}
        <Route path='/monitor/app-management' component={AppManagement} />

        <Route path='/monitor/dashboard/:id' exact component={DashboardDetail} />
        <Route path='/monitor/dashboards/:id' exact component={DashboardDetail} />
        <Route path='/monitor/dashboards/share/:id' component={DashboardShare} />
        <Route path='/monitor/dashboards' component={Dashboard} />
        <Route path='/monitor/chart/:ids' component={Chart} />
        <Route path='/monitor/indicator' component={IndicatorPage} />

        <Route exact path='/monitor/alert-rules/add/:group_id' component={StrategyAdd} />
        <Route exact path='/monitor/alert-rules/edit/:id' component={StrategyEdit} />
        <Route exact path='/monitor/alert-rules/:id?' component={Strategy} />
        <Route exact path='/monitor/alert-rules/brain/:id' component={StrategyBrain} />
        <Route exact path='/monitor/alert-mutes' component={Shield} />
        <Route exact path='/monitor/alert-mutes/add/:from?' component={AddShield} />
        <Route exact path='/monitor/alert-mutes/edit/:id' component={ShieldEdit} />
        <Route exact path='/monitor/alert-subscribes' component={Subscribe} />
        <Route exact path='/monitor/alert-subscribes/add' component={SubscribeAdd} />
        <Route exact path='/monitor/alert-subscribes/edit/:id' component={SubscribeEdit} />

        <Route exact path='/monitor/recording-rules/:id?' component={RecordingRule} />
        <Route exact path='/monitor/recording-rules/add/:group_id' component={RecordingRuleAdd} />
        <Route exact path='/monitor/recording-rules/edit/:id' component={RecordingRuleEdit} />

        <Route exact path='/monitor/alert-cur-events' component={Event} />
        <Route exact path='/monitor/alert-his-events' component={historyEvents} />
        <Route exact path='/monitor/alert-cur-events/:eventId' component={EventDetail} />
        <Route exact path='/monitor/alert-his-events/:eventId' component={EventDetail} />
        <Route exact path='/monitor/targets' component={MonObjectManage} />

        <Route exact path='/monitor/job-tpls' component={TaskTpl} />
        <Route exact path='/monitor/job-tpls/add' component={TaskTplAdd} />
        <Route exact path='/monitor/job-tpls/add/task' component={TaskAdd} />
        <Route exact path='/monitor/job-tpls/:id/detail' component={TaskTplDetail} />
        <Route exact path='/monitor/job-tpls/:id/modify' component={TaskTplModify} />
        <Route exact path='/monitor/job-tpls/:id/clone' component={TaskTplClone} />
        <Route exact path='/monitor/job-tasks' component={Task} />
        <Route exact path='/monitor/job-tasks/add' component={TaskAdd} />
        <Route exact path='/monitor/job-tasks/:id/result' component={TaskResult} />
        <Route exact path='/monitor/job-tasks/:id/detail' component={TaskDetail} />

        <Route exact path='/monitor/help/version' component={Version} />
        <Route exact path='/monitor/help/contact' component={Contact} />
        <Route exact path='/monitor/help/migrate' component={Migrate} />
        <Route exact path='/monitor/help/servers' component={Servers} />
        <Route exact path='/monitor/help/source' component={Datasource} />
        <Route exact path='/monitor/help/source/:action/:cate/:type' component={DatasourceAdd} />
        <Route exact path='/monitor/help/source/:action/:cate/:type/:id' component={DatasourceAdd} />

        {lazyRoutes.map((route, i) => (
          <RouteWithSubRoutes key={i} {...route} />
        ))}
        <Route path='/monitor/' exact>
          <Redirect to='/monitor/metric/explorer' />
        </Route>
        <Route path='/monitor/403' component={Page403} />
        <Route path='/monitor/404' component={NotFound} />
        <Route path='*' component={NotFound} />
      </Switch>
    </div>
  );
}
