import React, { useState, useEffect } from 'react';
import { Card, Table, Tag, Space, Input, Button, Modal, Form, Tabs, Row, Col, Typography, message, Tooltip, Select } from 'antd';
import { SearchOutlined, PlusOutlined, EditOutlined, DeleteOutlined, KeyOutlined, EyeOutlined, EyeInvisibleOutlined, ReloadOutlined, ApiOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import PageLayout from '@/components/pageLayout';
import BreadCrumb from '@/components/BreadCrumb';
import { getApplications, createApplication, deleteApplication, updateApplication, testDifyConnection } from '@/services/appManagement';
import './style.less';

const { Title } = Typography;
const { TabPane } = Tabs;
const { Search } = Input;
const { Option } = Select;

// 定义应用类型
const APP_TYPES = [
  { value: 'chat', label: '对话型', color: 'blue', description: '基于大语言模型的对话应用' },
  { value: 'completion', label: '文本生成型', color: 'green', description: '用于生成文本内容的应用' },
  { value: 'embedding', label: '向量型', color: 'purple', description: '用于文本嵌入和语义搜索的应用' },
  { value: 'assistant', label: '助手型', color: 'orange', description: '具有特定功能的AI助手应用' },
  { value: 'other', label: '其他', color: 'default', description: '其他类型的应用' }
];

// 定义应用接口
interface Application {
  id: number;
  name: string;
  appCode: string;
  description: string;
  apiKey: string;
  apiUrl: string;
  appType?: string; // 添加应用类型字段
}

const AppManagement: React.FC = () => {
  const { t } = useTranslation();
  const [applications, setApplications] = useState<Application[]>([]);
  const [searchText, setSearchText] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingApp, setEditingApp] = useState<Application | null>(null);
  const [form] = Form.useForm();
  const [visibleKeys, setVisibleKeys] = useState<{[key: number]: boolean}>({});
  const [loading, setLoading] = useState(false);

  // 直接使用 fetch 测试 API
  const testApiWithFetch = () => {
    console.log('开始使用 fetch 测试 API');
    
    fetch('/monitor/api/check/applications', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    })
    .then(response => {
      console.log('fetch 响应状态:', response.status);
      console.log('fetch 响应头:', response.headers);
      return response.json();
    })
    .then(data => {
      console.log('fetch 响应数据:', data);
      if (data && data.code === 0 && data.data) {
        console.log('fetch 获取的应用列表:', data.data.list);
        setApplications(data.data.list);
      }
    })
    .catch(error => {
      console.error('fetch 请求错误:', error);
      console.error('错误详情:', {
        message: error.message,
        name: error.name,
        stack: error.stack
      });
    });
  };

  // 获取应用列表
  const fetchApplications = async () => {
    setLoading(true);
    try {
      console.log('开始获取应用列表');
      
      getApplications()
        .then(data => {
          console.log('API响应数据:', data);
          if (data && data.code === 0 && data.data) {
            console.log('应用列表数据:', data.data.list);
            setApplications(data.data.list);
          } else {
            // 如果API请求成功但返回错误码，使用模拟数据
            console.error('API返回错误码，使用模拟数据:', data);
            useMockData();
          }
        })
        .catch(error => {
          console.error('API请求异常:', error);
          // 如果API请求失败，使用模拟数据
          useMockData();
        })
        .finally(() => {
          setLoading(false);
        });
    } catch (error) {
      console.error('获取应用列表异常:', error);
      useMockData();
      setLoading(false);
    }
  };

  // 使用模拟数据的函数
  const useMockData = () => {
    const mockData = [
      {
        id: 1,
        name: 'ChatGPT助手',
        appCode: 'chatgpt-assistant',
        description: '基于OpenAI的聊天机器人应用，可以回答问题、生成内容和提供智能对话',
        apiKey: 'sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
        apiUrl: 'https://api.dify.ai/v1/chat'
      },
      {
        id: 2,
        name: '文档分析器',
        appCode: 'doc-analyzer',
        description: '用于分析和提取文档内容的应用，支持多种文档格式，包括PDF、Word和Excel',
        apiKey: 'sk-yyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyy',
        apiUrl: 'https://api.dify.ai/v1/documents'
      },
      {
        id: 3,
        name: '代码助手',
        appCode: 'code-helper',
        description: '帮助开发人员编写和优化代码的智能工具，支持多种编程语言',
        apiKey: 'sk-zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz',
        apiUrl: 'https://api.dify.ai/v1/code'
      },
      {
        id: 4,
        name: '数据可视化',
        appCode: 'data-viz',
        description: '将复杂数据转换为直观图表和仪表盘的工具，支持实时数据更新',
        apiKey: 'sk-aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa',
        apiUrl: 'https://api.dify.ai/v1/visualization'
      },
      {
        id: 5,
        name: '智能翻译',
        appCode: 'ai-translator',
        description: '基于AI的多语言翻译工具，支持100多种语言之间的实时翻译',
        apiKey: 'sk-bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb',
        apiUrl: 'https://api.dify.ai/v1/translate'
      }
    ];
    setApplications(mockData);
  };

  // 组件挂载时获取应用列表
  useEffect(() => {
    fetchApplications();
  }, []);

  // 过滤应用列表
  const filteredApplications = applications.filter(app => 
    app.name.toLowerCase().includes(searchText.toLowerCase()) ||
    app.description.toLowerCase().includes(searchText.toLowerCase()) ||
    app.appCode.toLowerCase().includes(searchText.toLowerCase()) ||
    (app.apiUrl && app.apiUrl.toLowerCase().includes(searchText.toLowerCase()))
  );

  // 显示/隐藏API Key
  const toggleKeyVisibility = (id: number) => {
    setVisibleKeys({
      ...visibleKeys,
      [id]: !visibleKeys[id]
    });
  };
  
  // 打开编辑/创建模态框
  const showModal = (app?: Application) => {
    setEditingApp(app || null);
    if (app) {
      form.setFieldsValue({
        name: app.name,
        appCode: app.appCode,
        description: app.description,
        apiKey: app.apiKey,
        apiUrl: app.apiUrl,
      });
    } else {
      form.resetFields();
    }
    setIsModalVisible(true);
  };

  // 检查 appCode 是否唯一
  const checkAppCodeUnique = (appCode: string) => {
    return !applications.some(app => app.appCode === appCode && app.id !== (editingApp?.id || 0));
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      // 检查 appCode 唯一性
      if (!checkAppCodeUnique(values.appCode)) {
        message.error(t('应用标识码已存在，请使用其他标识码'));
        return;
      }
      
      console.log('提交创建应用数据:', values);
      
      if (editingApp) {
        // 更新应用
        const response = await updateApplication(editingApp.id, values);
        console.log('更新应用响应:', response);
        
        if (response && response.code === 0) {
          message.success(t('应用已更新'));
          setIsModalVisible(false);
          fetchApplications(); // 刷新列表
        } else {
          console.error('更新应用失败:', response);
          message.error(t('更新应用失败'));
        }
      } else {
        // 创建新应用
        const response = await createApplication(values);
        console.log('创建应用响应:', response);
        
        if (response && response.code === 0) {
          message.success(t('应用已创建'));
          setIsModalVisible(false);
          fetchApplications(); // 刷新列表
        } else {
          console.error('创建应用失败:', response);
          message.error(t('创建应用失败'));
        }
      }
    } catch (error) {
      console.error('提交表单异常:', error);
      message.error(t('提交表单失败'));
    }
  };

  // 删除应用
  const handleDelete = (id: number) => {
    Modal.confirm({
      title: t('确认删除'),
      content: t('删除后无法恢复，确定要删除此应用吗？'),
      onOk: async () => {
        try {
          console.log('删除应用ID:', id);
          
          const response = await deleteApplication(id);
          console.log('删除应用响应:', response);
          
          if (response && response.code === 0) {
            message.success(t('应用已删除'));
            fetchApplications(); // 刷新列表
          } else {
            console.error('删除应用失败:', response);
            message.error(t('删除应用失败'));
          }
        } catch (error) {
          console.error('删除应用异常:', error);
          message.error(t('删除应用失败'));
        }
      },
    });
  };

  // 添加测试Dify连通性的函数
  const handleTestConnection = async (app: Application) => {
    message.loading({ content: t('正在测试连接...'), key: 'testConnection' });
    
    try {
      const result = await testDifyConnection(app.apiKey, app.apiUrl);
      
      if (result.success) {
        // 成功状态 - 使用Modal显示更详细的成功信息
        Modal.success({
          title: t('连接测试成功'),
          content: (
            <div>
              <p>{t('成功连接到Dify API服务')}</p>
              {result.data && result.data.name && (
                <p>{t('服务名称')}: <strong>{result.data.name}</strong></p>
              )}
              <p>{t('API地址')}: <code>{app.apiUrl}</code></p>
              <p>{t('API密钥验证通过')}</p>
            </div>
          ),
          okText: t('确定'),
        });
      } else {
        // 失败状态 - 使用Modal显示详细的错误信息
        Modal.error({
          title: t('连接测试失败'),
          content: (
            <div>
              <p>{t('无法连接到Dify API服务')}</p>
              <p>{t('错误信息')}: <strong>{result.error || t('未知错误')}</strong></p>
              <p>{t('请检查以下内容')}:</p>
              <ul>
                <li>{t('API地址是否正确')}: <code>{app.apiUrl}</code></li>
                <li>{t('API密钥是否有效')}</li>
                <li>{t('网络连接是否正常')}</li>
                <li>{t('Dify服务是否在线')}</li>
              </ul>
            </div>
          ),
          okText: t('确定'),
        });
      }
    } catch (error) {
      // 异常状态 - 显示异常信息
      Modal.error({
        title: t('连接测试异常'),
        content: (
          <div>
            <p>{t('测试过程中发生异常')}</p>
            <p>{t('异常信息')}: <strong>{error instanceof Error ? error.message : String(error)}</strong></p>
            <p>{t('请检查网络连接或联系系统管理员')}</p>
          </div>
        ),
        okText: t('确定'),
      });
    }
  };

  // 修改表格列定义，添加应用类型列
  const columns = [
    {
      title: t('应用名称'),
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => <span>{text}</span>,
    },
    {
      title: t('应用标识码'),
      dataIndex: 'appCode',
      key: 'appCode',
      render: (text: string) => <span style={{ fontFamily: 'monospace' }}>{text}</span>,
    },
    {
      title: t('应用类型'),
      dataIndex: 'appType',
      key: 'appType',
      render: (type: string) => {
        const appType = APP_TYPES.find(t => t.value === type) || APP_TYPES[4]; // 默认为"其他"
        return <Tag color={appType.color}>{appType.label}</Tag>;
      },
      filters: APP_TYPES.map(type => ({ text: type.label, value: type.value })),
      onFilter: (value: string, record: Application) => record.appType === value || (!record.appType && value === 'other'),
    },
    {
      title: t('描述'),
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: t('API Key'),
      dataIndex: 'apiKey',
      key: 'apiKey',
      render: (text: string, record: Application) => (
        <Space>
          <span style={{ fontFamily: 'monospace' }}>
            {visibleKeys[record.id] ? text : '••••••••••••••••••••••••••••••••'}
          </span>
          <Button 
            type="text" 
            icon={visibleKeys[record.id] ? <EyeInvisibleOutlined /> : <EyeOutlined />} 
            onClick={() => toggleKeyVisibility(record.id)}
          />
        </Space>
      ),
    },
    {
      title: t('Dify 接口地址'),
      dataIndex: 'apiUrl',
      key: 'apiUrl',
      render: (text: string) => (
        <Space>
          <span style={{ fontFamily: 'monospace' }}>{text}</span>
        </Space>
      ),
    },
    {
      title: t('操作'),
      key: 'action',
      render: (_: any, record: Application) => (
        <Space size="middle">
          <Tooltip title={t('测试连接')}>
            <Button 
              type="primary" 
              shape="circle" 
              icon={<ApiOutlined />} 
              size="small"
              onClick={() => handleTestConnection(record)}
              className="action-button test-button"
              style={{ backgroundColor: '#52c41a' }}
            />
          </Tooltip>
          <Tooltip title={t('编辑')}>
            <Button 
              type="primary" 
              shape="circle" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => showModal(record)}
              className="action-button edit-button"
            />
          </Tooltip>
          <Tooltip title={t('删除')}>
            <Button 
              type="primary" 
              danger 
              shape="circle" 
              icon={<DeleteOutlined />} 
              size="small"
              onClick={() => handleDelete(record.id)}
              className="action-button delete-button"
            />
          </Tooltip>
        </Space>
      ),
      width: 180,
      align: 'center'
    }
  ];

  return (
    <PageLayout title={t('应用管理')} hideCluster>
      <div className="app-management-container">
        <Card>
          <div className="app-management-header">
            <Title level={4}>{t('Dify 应用管理')}</Title>
            <div className="app-management-actions">
              <Search
                placeholder={t('搜索应用名称或描述')}
                allowClear
                style={{ width: 300, marginRight: 16 }}
                onChange={(e) => setSearchText(e.target.value)}
              />
              <Button 
                icon={<ReloadOutlined />} 
                onClick={fetchApplications}
                style={{ marginRight: 16 }}
                loading={loading}
              >
                {t('刷新')}
              </Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={() => showModal()}>
                {t('创建应用')}
              </Button>
            </div>
          </div>
          
          <Table 
            columns={columns} 
            dataSource={filteredApplications} 
            rowKey="id"
            pagination={{ pageSize: 10 }}
            loading={loading}
          />
        </Card>
      </div>
      
      {/* 创建/编辑应用模态框 */}
      <Modal
        title={editingApp ? t('编辑应用') : t('创建应用')}
        visible={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        onOk={handleSubmit}
        width={700}
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label={t('应用名称')}
                rules={[{ required: true, message: t('请输入应用名称') }]}
              >
                <Input placeholder={t('请输入应用名称')} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="appCode"
                label={t('应用标识码')}
                rules={[
                  { required: true, message: t('请输入应用标识码') },
                  { pattern: /^[a-z0-9-]+$/, message: t('应用标识码只能包含小写字母、数字和连字符') }
                ]}
                extra={t('应用标识码作为唯一标识，创建后不可修改')}
              >
                <Input placeholder={t('请输入应用标识码')} disabled={!!editingApp} />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="appType"
            label={t('应用类型')}
            rules={[{ required: true, message: t('请选择应用类型') }]}
            initialValue="chat"
          >
            <Select placeholder={t('请选择应用类型')}>
              {APP_TYPES.map(type => (
                <Option key={type.value} value={type.value}>
                  <Space>
                    <Tag color={type.color}>{type.label}</Tag>
                    <span>{type.description}</span>
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="description"
            label={t('应用描述')}
            rules={[{ required: true, message: t('请输入应用描述') }]}
          >
            <Input.TextArea rows={3} placeholder={t('请输入应用描述')} />
          </Form.Item>
          
          <Form.Item
            name="apiKey"
            label={t('API Key')}
            rules={[{ required: true, message: t('请输入API Key') }]}
            extra={t('API Key用于应用认证，请妥善保管')}
          >
            <Input.Password placeholder={t('请输入API Key')} />
          </Form.Item>
          
          <Form.Item
            name="apiUrl"
            label={t('Dify 接口地址')}
            rules={[
              { required: true, message: t('请输入Dify接口地址') },
              { type: 'url', message: t('请输入有效的URL地址') }
            ]}
            extra={t('Dify API接口地址，例如：https://api.dify.ai/v1/chat')}
          >
            <Input placeholder={t('请输入Dify接口地址')} />
          </Form.Item>
        </Form>
      </Modal>
    </PageLayout>
  );
};

export default AppManagement;