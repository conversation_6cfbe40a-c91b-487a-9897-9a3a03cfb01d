.app-management-container {
  padding: 16px;
  
  .app-management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .app-management-actions {
      display: flex;
      align-items: center;
    }
  }
  
  .ant-table-cell {
    vertical-align: middle;
  }
}

// 操作按钮样式
.action-button {
  transition: all 0.3s;
  
  &:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}

.edit-button {
  background-color: #1890ff;
  
  &:hover {
    background-color: #40a9ff;
  }
}

.delete-button {
  background-color: #ff4d4f;
  
  &:hover {
    background-color: #ff7875;
  }
}

.test-button {
  background-color: #52c41a;
  
  &:hover {
    background-color: #73d13d;
  }
}

// 表格行悬停效果
.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}