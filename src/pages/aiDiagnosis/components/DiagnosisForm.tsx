import React from 'react';
import { Form, Input } from 'antd';

interface DiagnosisFormProps {
  form: any;
}

const DiagnosisForm: React.FC<DiagnosisFormProps> = ({ form }) => {
  return (
    <Form form={form} layout="vertical">
      <Form.Item
        name="name"
        label="模块名称"
        rules={[{ required: true, message: '请输入模块名称' }]}
      >
        <Input />
      </Form.Item>
      <Form.Item
        name="description"
        label="描述"
      >
        <Input.TextArea />
      </Form.Item>
      <Form.Item
        name="difyConfig"
        label="Dify API 配置"
        rules={[
          { required: true, message: '请输入 Dify API 配置' },
          {
            validator: async (_, value) => {
              try {
                const config = JSON.parse(value);
                if (!config.apiKey) throw new Error('缺少 API Key');
                if (!config.endpoint) throw new Error('缺少 Endpoint');
                if (!config.workflowId) throw new Error('缺少 Workflow ID');
              } catch (e) {
                throw new Error('配置格式不正确或缺少必要字段');
              }
            },
          },
        ]}
      >
        <Input.TextArea
          rows={6}
          placeholder={`请输入 JSON 格式的配置，例如：
{
  "apiKey": "app-xxx",
  "endpoint": "http://localhost/v1",
  "workflowId": "xxx",
  "agentId": ""
}`}
        />
      </Form.Item>
    </Form>
  );
};

export default DiagnosisForm;
