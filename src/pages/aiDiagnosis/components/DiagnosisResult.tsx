import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Spin, Alert, Typo<PERSON>, Row, Col, Tag, Button, message } from 'antd';
import { ReloadOutlined, CopyOutlined } from '@ant-design/icons';
import { runDiagnosis } from '@/services/aiDiagnosis';
import '../../../styles/diagnostic-result.css';

const { Title, Text } = Typography;

interface DiagnosisResultProps {
  visible: boolean;
  onClose: () => void;
  module: any;
}

const DiagnosisResult: React.FC<DiagnosisResultProps> = ({ visible, onClose, module }) => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (visible && module) {
      runDiagnosisTask();
    }
  }, [visible, module]);

  const runDiagnosisTask = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const diagnosisResult = await runDiagnosis(module);
      setResult(diagnosisResult);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '诊断执行失败';
      setError(errorMessage);
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleRetry = () => {
    runDiagnosisTask();
  };

  const handleCopyResult = () => {
    if (result?.data?.outputs?.text) {
      navigator.clipboard.writeText(result.data.outputs.text);
      message.success('诊断结果已复制到剪贴板');
    }
  };

  const renderDiagnosticResult = (resultData: any) => {
    if (!resultData?.data?.outputs?.text) {
      return <Text>暂无诊断结果</Text>;
    }

    const text = resultData.data.outputs.text;
    
    // 简单的Markdown渲染
    const renderMarkdown = (content: string) => {
      return (
        <div 
          className="diagnostic-result"
          dangerouslySetInnerHTML={{ 
            __html: content
              .replace(/\n/g, '<br>')
              .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
              .replace(/\*(.*?)\*/g, '<em>$1</em>')
              .replace(/`(.*?)`/g, '<code class="diagnostic-inline-code">$1</code>')
              .replace(/```([\s\S]*?)```/g, '<pre class="diagnostic-code-block"><code>$1</code></pre>')
              .replace(/^### (.*$)/gim, '<h3>$1</h3>')
              .replace(/^## (.*$)/gim, '<h2>$1</h2>')
              .replace(/^# (.*$)/gim, '<h1>$1</h1>')
          }} 
        />
      );
    };

    return renderMarkdown(text);
  };

  return (
    <Modal
      title={`诊断结果 - ${module?.name}`}
      visible={visible}
      onCancel={onClose}
      width={900}
      footer={[
        <Button key="retry" icon={<ReloadOutlined />} onClick={handleRetry} loading={loading}>
          重新诊断
        </Button>,
        <Button key="copy" icon={<CopyOutlined />} onClick={handleCopyResult} disabled={!result}>
          复制结果
        </Button>,
        <Button key="close" onClick={onClose}>
          关闭
        </Button>
      ]}
    >
      {loading && (
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px' }}>
            <Text>正在执行诊断，请稍候...</Text>
          </div>
        </div>
      )}

      {error && (
        <Alert
          type="error"
          message="诊断执行失败"
          description={error}
          style={{ marginBottom: '16px' }}
          action={
            <Button size="small" onClick={handleRetry}>
              重试
            </Button>
          }
        />
      )}

      {result && !loading && (
        <div>
          <Row gutter={16} style={{ marginBottom: '16px' }}>
            <Col span={6}>
              <Text strong>状态: </Text>
              <Tag color={result.data?.status === 'succeeded' ? 'green' : 'red'}>
                {result.data?.status || 'unknown'}
              </Tag>
            </Col>
            <Col span={6}>
              <Text strong>耗时: </Text>
              <Text>{result.data?.elapsed_time || 0}s</Text>
            </Col>
            <Col span={6}>
              <Text strong>Token消耗: </Text>
              <Text>{result.data?.total_tokens || 0}</Text>
            </Col>
            <Col span={6}>
              <Text strong>步骤数: </Text>
              <Text>{result.data?.total_steps || 0}</Text>
            </Col>
          </Row>
          
          <div style={{ marginTop: '16px' }}>
            <Title level={4}>诊断内容</Title>
            <div style={{ 
              maxHeight: '500px', 
              overflowY: 'auto',
              border: '1px solid #d9d9d9',
              borderRadius: '6px',
              padding: '16px',
              backgroundColor: '#fafafa'
            }}>
              {renderDiagnosticResult(result)}
            </div>
          </div>

          {result.data?.error && (
            <Alert
              type="warning"
              message="执行过程中出现警告"
              description={result.data.error}
              style={{ marginTop: '16px' }}
            />
          )}
        </div>
      )}
    </Modal>
  );
};

export default DiagnosisResult;
