import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Button, 
  Table, 
  Modal, 
  Form, 
  Input, 
  Select, 
  message, 
  Spin, 
  Typography, 
  Space, 
  Tabs, 
  Row, 
  Col,
  Tag,
  Popconfirm
} from 'antd';
import { 
  PlusOutlined, 
  PlayCircleOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  EyeOutlined,
  SettingOutlined,
  RobotOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { DiagnosisModule, DiagnosticResult } from '@/types/diagnosis';
import { diagnosisModuleManager, DifyService } from '@/services/difyService';
import '../../styles/diagnostic-result.css';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;

const AiDiagnosis: React.FC = () => {
  const { t } = useTranslation();
  const [modules, setModules] = useState<DiagnosisModule[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingModule, setEditingModule] = useState<DiagnosisModule | null>(null);
  const [resultModalVisible, setResultModalVisible] = useState(false);
  const [diagnosticResult, setDiagnosticResult] = useState<DiagnosticResult | null>(null);
  const [runningModules, setRunningModules] = useState<Set<string>>(new Set());
  const [form] = Form.useForm();

  useEffect(() => {
    loadModules();
  }, []);

  const loadModules = () => {
    setModules(diagnosisModuleManager.getModules());
  };

  const handleAddModule = () => {
    setEditingModule(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEditModule = (module: DiagnosisModule) => {
    setEditingModule(module);
    form.setFieldsValue(module);
    setModalVisible(true);
  };

  const handleDeleteModule = (moduleId: string) => {
    diagnosisModuleManager.removeModule(moduleId);
    loadModules();
    message.success(t('删除成功'));
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingModule) {
        diagnosisModuleManager.updateModule(editingModule.id, values);
        message.success(t('更新成功'));
      } else {
        const newModule: DiagnosisModule = {
          id: `module-${Date.now()}`,
          ...values,
        };
        diagnosisModuleManager.addModule(newModule);
        message.success(t('添加成功'));
      }
      
      setModalVisible(false);
      loadModules();
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  };

  const handleRunDiagnosis = async (module: DiagnosisModule) => {
    setRunningModules(prev => new Set(prev).add(module.id));
    
    try {
      const difyService = new DifyService(module.difyConfig);
      
      // 测试连接
      const isConnected = await difyService.testConnection();
      if (!isConnected) {
        message.error(t('无法连接到Dify服务，请检查配置'));
        return;
      }

      // 执行诊断
      const result = await difyService.runWorkflow({
        module_name: module.name,
        description: module.description,
        timestamp: new Date().toISOString(),
      });

      setDiagnosticResult(result);
      setResultModalVisible(true);
      message.success(t('诊断完成'));
    } catch (error) {
      console.error('Diagnosis failed:', error);
      message.error(t('诊断失败: ') + (error as Error).message);
    } finally {
      setRunningModules(prev => {
        const newSet = new Set(prev);
        newSet.delete(module.id);
        return newSet;
      });
    }
  };

  const renderDiagnosticResult = (result: DiagnosticResult) => {
    if (!result.outputs?.text) {
      return <Text>{t('暂无诊断结果')}</Text>;
    }

    // 简单的Markdown渲染
    const renderMarkdown = (text: string) => {
      return (
        <div 
          className="diagnostic-result"
          dangerouslySetInnerHTML={{ 
            __html: text
              .replace(/\n/g, '<br>')
              .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
              .replace(/\*(.*?)\*/g, '<em>$1</em>')
              .replace(/`(.*?)`/g, '<code class="diagnostic-inline-code">$1</code>')
          }} 
        />
      );
    };

    return renderMarkdown(result.outputs.text);
  };

  const columns = [
    {
      title: t('模块名称'),
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => <Text strong>{text}</Text>,
    },
    {
      title: t('描述'),
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: t('状态'),
      key: 'status',
      render: (_, record: DiagnosisModule) => (
        <Tag color={record.difyConfig.apiKey ? 'green' : 'red'}>
          {record.difyConfig.apiKey ? t('已配置') : t('未配置')}
        </Tag>
      ),
    },
    {
      title: t('操作'),
      key: 'actions',
      render: (_, record: DiagnosisModule) => (
        <Space>
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            size="small"
            loading={runningModules.has(record.id)}
            onClick={() => handleRunDiagnosis(record)}
            disabled={!record.difyConfig.apiKey}
          >
            {t('运行诊断')}
          </Button>
          <Button
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEditModule(record)}
          >
            {t('编辑')}
          </Button>
          <Popconfirm
            title={t('确定要删除这个模块吗？')}
            onConfirm={() => handleDeleteModule(record.id)}
            okText={t('确定')}
            cancelText={t('取消')}
          >
            <Button
              icon={<DeleteOutlined />}
              size="small"
              danger
            >
              {t('删除')}
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={3} style={{ margin: 0 }}>
            <RobotOutlined style={{ marginRight: '8px' }} />
            {t('AI系统诊断')}
          </Title>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddModule}
          >
            {t('添加诊断模块')}
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={modules}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Card>

      {/* 添加/编辑模块弹窗 */}
      <Modal
        title={editingModule ? t('编辑诊断模块') : t('添加诊断模块')}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label={t('模块名称')}
            rules={[{ required: true, message: t('请输入模块名称') }]}
          >
            <Input placeholder={t('请输入模块名称')} />
          </Form.Item>
          
          <Form.Item
            name="description"
            label={t('描述')}
            rules={[{ required: true, message: t('请输入描述') }]}
          >
            <TextArea rows={3} placeholder={t('请输入模块描述')} />
          </Form.Item>

          <Form.Item
            name={['difyConfig', 'endpoint']}
            label={t('Dify端点')}
            rules={[{ required: true, message: t('请输入Dify端点') }]}
          >
            <Input placeholder="https://api.dify.ai/v1" />
          </Form.Item>

          <Form.Item
            name={['difyConfig', 'apiKey']}
            label={t('API密钥')}
            rules={[{ required: true, message: t('请输入API密钥') }]}
          >
            <Input.Password placeholder={t('请输入API密钥')} />
          </Form.Item>

          <Form.Item
            name={['difyConfig', 'workflowId']}
            label={t('工作流ID')}
          >
            <Input placeholder={t('请输入工作流ID（可选）')} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 诊断结果弹窗 */}
      <Modal
        title={t('诊断结果')}
        open={resultModalVisible}
        onCancel={() => setResultModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setResultModalVisible(false)}>
            {t('关闭')}
          </Button>
        ]}
        width={800}
      >
        {diagnosticResult && (
          <div>
            <Row gutter={16} style={{ marginBottom: '16px' }}>
              <Col span={8}>
                <Text strong>{t('状态')}: </Text>
                <Tag color={diagnosticResult.status === 'succeeded' ? 'green' : 'red'}>
                  {diagnosticResult.status}
                </Tag>
              </Col>
              <Col span={8}>
                <Text strong>{t('耗时')}: </Text>
                <Text>{diagnosticResult.elapsed_time}s</Text>
              </Col>
              <Col span={8}>
                <Text strong>{t('Token消耗')}: </Text>
                <Text>{diagnosticResult.total_tokens}</Text>
              </Col>
            </Row>
            
            <div style={{ marginTop: '16px' }}>
              <Title level={4}>{t('诊断内容')}</Title>
              {renderDiagnosticResult(diagnosticResult)}
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default AiDiagnosis;
