import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Modal, Form, Input, message, Space, Upload, Tooltip, Alert } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, DownloadOutlined, UploadOutlined, SyncOutlined } from '@ant-design/icons';
import {
  getDiagnosisModules,
  createDiagnosisModule,
  updateDiagnosisModule,
  deleteDiagnosisModule,
  exportDiagnosisModulesFile
} from '@/services/aiDiagnosis';
import DiagnosisResult from './components/DiagnosisResult';
import type { UploadProps } from 'antd';
import type { RcFile } from 'antd/es/upload';

const AiDiagnosis: React.FC = () => {
  const [modules, setModules] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [form] = Form.useForm();
  const [selectedModule, setSelectedModule] = useState<any>(null);
  const [showResult, setShowResult] = useState(false);
  const [showExportTip, setShowExportTip] = useState(false);

  useEffect(() => {
    fetchModules();
  }, []);

  const fetchModules = async () => {
    setLoading(true);
    try {
      const data = await getDiagnosisModules();
      setModules(data);
    } catch (error) {
      message.error('获取系统诊断失败');
    } finally {
      setLoading(false);
    }
  };

  const handleImportConfig = async (file: RcFile) => {
    try {
      const fileContent = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsText(file);
      });

      const importedModules = JSON.parse(fileContent);

      if (!Array.isArray(importedModules)) {
        throw new Error('导入的配置格式不正确，应为数组');
      }

      // 验证每个模块的格式
      importedModules.forEach((module, index) => {
        if (!module.id || !module.name || !module.difyConfig) {
          throw new Error(`第 ${index + 1} 个模块缺少必要字段 (id, name, difyConfig)`);
        }
      });

      // 设置当前状态并导出文件
      setModules(importedModules);
      exportDiagnosisModulesFile(importedModules);
      message.success('配置导入成功，请替换服务器上的配置文件');
      setShowExportTip(true);
    } catch (error) {
      message.error('导入失败: ' + (error instanceof Error ? error.message : '未知错误'));
    }
    return false; // 阻止默认上传行为
  };

  const uploadProps: UploadProps = {
    accept: '.json',
    beforeUpload: handleImportConfig,
    showUploadList: false,
  };

  const columns = [
    {
      title: '模块名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'API配置',
      key: 'difyConfig',
      render: (record: any) => {
        const config = typeof record.difyConfig === 'string'
          ? JSON.parse(record.difyConfig)
          : record.difyConfig;
        return (
          <div>
            <div>Endpoint: {config.endpoint}</div>
            <div>Workflow ID: {config.workflowId || 'N/A'}</div>
          </div>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (record: any) => (
        <Space>
          <Button
            type="primary"
            size="small"
            onClick={() => handleRunDiagnosis(record)}
          >
            运行诊断
          </Button>
          <Button
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          />
          <Button
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          />
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setEditingId(null);
    form.resetFields();
    setVisible(true);
  };

  const handleEdit = (record: any) => {
    setEditingId(record.id);
    form.setFieldsValue({
      name: record.name,
      description: record.description,
      difyConfig: typeof record.difyConfig === 'string'
        ? record.difyConfig
        : JSON.stringify(record.difyConfig, null, 2),
    });
    setVisible(true);
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteDiagnosisModule(id);
      message.success('删除成功，请替换服务器上的配置文件');
      setShowExportTip(true);
      fetchModules();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      // 确保 difyConfig 是有效的 JSON 字符串
      let difyConfig;
      try {
        difyConfig = typeof values.difyConfig === 'string'
          ? JSON.parse(values.difyConfig)
          : values.difyConfig;
      } catch (e) {
        throw new Error('Dify 配置格式不正确，请检查 JSON 格式');
      }

      // 验证必要字段
      if (!difyConfig.apiKey) {
        throw new Error('请填写 API Key');
      }
      if (!difyConfig.endpoint) {
        throw new Error('请填写 Endpoint');
      }
      if (!difyConfig.workflowId) {
        throw new Error('请填写 Workflow ID');
      }

      // 确保 endpoint 格式正确
      if (!difyConfig.endpoint.startsWith('http')) {
        difyConfig.endpoint = `http://${difyConfig.endpoint}`;
      }

      // 移除 endpoint 末尾的斜杠
      difyConfig.endpoint = difyConfig.endpoint.replace(/\/+$/, '');

      // 更新配置
      values.difyConfig = JSON.stringify(difyConfig);

      if (editingId) {
        await updateDiagnosisModule(editingId, values);
        message.success('更新成功，请替换服务器上的配置文件');
      } else {
        await createDiagnosisModule(values);
        message.success('创建成功，请替换服务器上的配置文件');
      }
      setVisible(false);
      setShowExportTip(true);
      fetchModules(); // 刷新列表以保持数据一致性
    } catch (error) {
      message.error('操作失败: ' + (error instanceof Error ? error.message : '未知错误'));
    }
  };

  const handleRunDiagnosis = (module: any) => {
    setSelectedModule(module);
    setShowResult(true);
  };

  const handleExportConfig = () => {
    exportDiagnosisModulesFile(modules);
    message.success('配置文件已导出，请替换服务器上的配置文件');
  };

  const columns = [
    {
      title: t('模块名称'),
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => <Text strong>{text}</Text>,
    },
    {
      title: t('描述'),
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: t('状态'),
      key: 'status',
      render: (_, record: DiagnosisModule) => (
        <Tag color={record.difyConfig.apiKey ? 'green' : 'red'}>
          {record.difyConfig.apiKey ? t('已配置') : t('未配置')}
        </Tag>
      ),
    },
    {
      title: t('操作'),
      key: 'actions',
      render: (_, record: DiagnosisModule) => (
        <Space>
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            size="small"
            loading={runningModules.has(record.id)}
            onClick={() => handleRunDiagnosis(record)}
            disabled={!record.difyConfig.apiKey}
          >
            {t('运行诊断')}
          </Button>
          <Button
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEditModule(record)}
          >
            {t('编辑')}
          </Button>
          <Popconfirm
            title={t('确定要删除这个模块吗？')}
            onConfirm={() => handleDeleteModule(record.id)}
            okText={t('确定')}
            cancelText={t('取消')}
          >
            <Button
              icon={<DeleteOutlined />}
              size="small"
              danger
            >
              {t('删除')}
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '20px' }}>
      {showExportTip && (
        <Alert
          type="warning"
          message="配置文件更新提醒"
          description="您已修改了系统诊断配置。请将导出的配置文件替换服务器上的 public/defaults/diagnosis-modules.json 文件，以使修改永久生效。"
          closable
          onClose={() => setShowExportTip(false)}
          style={{ marginBottom: '20px' }}
        />
      )}

      <Card
        title="智能诊断"
        extra={
          <Space>
            <Tooltip title="刷新模块列表">
              <Button
                icon={<SyncOutlined />}
                onClick={fetchModules}
                loading={loading}
              />
            </Tooltip>
            <Tooltip title="导出配置">
              <Button
                icon={<DownloadOutlined />}
                onClick={handleExportConfig}
              />
            </Tooltip>
            <Tooltip title="导入配置">
              <Upload {...uploadProps}>
                <Button icon={<UploadOutlined />} />
              </Upload>
            </Tooltip>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              添加系统诊断
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={modules}
          rowKey="id"
          loading={loading}
          locale={{ emptyText: '暂无配置，请添加系统诊断或导入配置文件' }}
        />
      </Card>

      <Modal
        title={editingId ? '编辑系统诊断' : '添加系统诊断'}
        visible={visible}
        onOk={handleSubmit}
        onCancel={() => setVisible(false)}
        width={600}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="模块名称"
            rules={[{ required: true, message: '请输入模块名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea />
          </Form.Item>
          <Form.Item
            name="difyConfig"
            label="Dify API 配置"
            rules={[
              { required: true, message: '请输入 Dify API 配置' },
              {
                validator: async (_, value) => {
                  try {
                    const config = JSON.parse(value);
                    if (!config.apiKey) throw new Error('缺少 API Key');
                    if (!config.endpoint) throw new Error('缺少 Endpoint');
                    if (!config.workflowId) throw new Error('缺少 Workflow ID');
                  } catch (e) {
                    throw new Error('配置格式不正确或缺少必要字段');
                  }
                },
              },
            ]}
          >
            <Input.TextArea
              placeholder={`请输入 JSON 格式的 Dify API 配置，例如：
{
  "apiKey": "your-api-key",
  "endpoint": "https://api.dify.ai/v1/chat-messages",
  "workflowId": "optional-workflow-id",
  "agentId": "optional-agent-id"
}`}
              rows={8}
            />
          </Form.Item>
          <Alert
            type="info"
            message="配置文件更新说明"
            description="修改配置后，系统会导出配置文件。您需要将此文件替换服务器上的配置文件，以使修改永久生效。"
            style={{ marginBottom: '16px' }}
          />
        </Form>
      </Modal>

      {showResult && selectedModule && (
        <DiagnosisResult
          visible={showResult}
          onClose={() => setShowResult(false)}
          module={selectedModule}
        />
      )}
    </div>
  );
};

export default AiDiagnosis;
