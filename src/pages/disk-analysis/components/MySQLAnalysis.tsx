import React, { useState, useEffect } from 'react';
import { Table, Tag, Space, Tooltip, Button, Card, Row, Col, Input, Spin, Typography, Tabs, Statistic, Empty, Alert, Modal, Radio } from 'antd';
import { InfoCircleOutlined, LineChartOutlined, SearchOutlined, SyncOutlined, RobotOutlined, DatabaseOutlined, AreaChartOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { mockDatabases } from '../mockData';
import { Line, Column } from '@ant-design/plots';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;

interface MySQLAnalysisProps {
  searchText: string;
}

// 定义趋势数据接口
interface TrendData {
  date: string;
  size: number;
  database: string;
}

// 定义每日增量数据接口
interface DailyIncrementData {
  date: string;
  increment: number;
  database: string;
}

import { usePrometheusData, useTrendData, useAIAnalysis } from './MySQLAnalysis/hooks';

import {
  DatabaseOverview,
  DatabaseList,
  LargeFilesList,
  AIAnalysisTab,
  TrendModal
} from './MySQLAnalysis/components';

import { MySQLAnalysisProps } from './MySQLAnalysis/types';

const MySQLAnalysis: React.FC<MySQLAnalysisProps> = ({ searchText }) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('data');
  const [localSearchText, setLocalSearchText] = useState(searchText);
  
  // 使用自定义hooks
  const {
    loading,
    databases,
    filteredDatabases,
    prometheusData,
    refreshData
  } = usePrometheusData(localSearchText);
  
  const {
    trendModalVisible,
    setTrendModalVisible,
    selectedDatabase,
    trendData,
    dailyIncrementData,
    timeRange,
    openTrendChart,
    handleTimeRangeChange
  } = useTrendData();
  
  const {
    aiLoading,
    aiAnalysisResult,
    chatMessages,
    userQuestion,
    setUserQuestion,
    analyzeWithDify,
    sendQuestion
  } = useAIAnalysis(filteredDatabases, prometheusData);
  
  return (
    <div className="mysql-analysis-container">
      <div className="mysql-page-header">
        <div className="mysql-page-title">
          <DatabaseOutlined />
          {t('MySQL增长分析')}
        </div>
      </div>
      
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={t('数据概览')} key="data">
          <DatabaseOverview 
            filteredDatabases={filteredDatabases}
            loading={loading}
            openTrendChart={openTrendChart}
          />
          
          <DatabaseList 
            filteredDatabases={filteredDatabases}
            loading={loading}
            localSearchText={localSearchText}
            setLocalSearchText={setLocalSearchText}
            openTrendChart={openTrendChart}
          />
          
          <LargeFilesList 
            loading={loading}
            openTrendChart={openTrendChart}
          />
        </TabPane>
        
        {/* <TabPane tab={t('智能分析')} key="ai">
          <AIAnalysisTab 
            aiLoading={aiLoading}
            aiAnalysisResult={aiAnalysisResult}
            analyzeWithDify={analyzeWithDify}
            trendData={trendData}
            chatMessages={chatMessages}
            userQuestion={userQuestion}
            setUserQuestion={setUserQuestion}
            sendQuestion={sendQuestion}
          />
        </TabPane> */}
      </Tabs>
      
      <TrendModal 
        visible={trendModalVisible}
        onCancel={() => setTrendModalVisible(false)}
        selectedDatabase={selectedDatabase}
        trendData={trendData}
        dailyIncrementData={dailyIncrementData}
        timeRange={timeRange}
        handleTimeRangeChange={handleTimeRangeChange}
      />
    </div>
  );
};

export default MySQLAnalysis;