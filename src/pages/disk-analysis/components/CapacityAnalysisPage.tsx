import React, { useState } from 'react';
import { Card, Tabs } from 'antd';
import { DatabaseOutlined, CloudServerOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import MySQLAnalysis from './MySQLAnalysis';
import ContainerAnalysis from './ContainerAnalysis';
import { mockDatabases, mockContainers } from '../mockData';

const { TabPane } = Tabs;

interface CapacityAnalysisPageProps {}

const CapacityAnalysisPage: React.FC<CapacityAnalysisPageProps> = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('mysql');
  const [searchText, setSearchText] = useState('');
  
  return (
    <div className="capacity-container">
      {/* 主内容区 */}
      <Card className="capacity-content-card">
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
        >
          <TabPane tab={<span><DatabaseOutlined /> {t('MySQL增长分析')}</span>} key="mysql">
            <MySQLAnalysis searchText={searchText} />
          </TabPane>
          
          <TabPane tab={<span><CloudServerOutlined /> {t('容器分析')}</span>} key="container">
            <ContainerAnalysis searchText={searchText} />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default CapacityAnalysisPage;