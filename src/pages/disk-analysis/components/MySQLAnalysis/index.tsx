import React, { useState, useEffect, useRef, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON>ton, Spin, message } from 'antd';
import { SyncOutlined, DatabaseOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

// 导入类型
import { Database, TrendData, DailyIncrementData } from './types';

// 导入hooks
import { usePrometheusData } from './hooks/usePrometheusData';
import { useAIAnalysis } from './hooks/useAIAnalysis';
import { useTrendData } from './hooks/useTrendData';

// 导入子组件
import DatabaseOverview from './components/DatabaseOverview';
import DatabaseList from './components/DatabaseList';
import LargeFilesList from './components/LargeFilesList';
import AIAnalysisTab from './components/AIAnalysisTab';
import TrendModal from './components/TrendModal';
import DatabaseDetail from './components/DatabaseDetail';

// 导入API服务
import { refreshDiskAnalysisData } from '../../../../services/diskAnalysisService';
import { getDatabaseOverviewTrendData } from '../../../services/diskAnalysisService';

// 导入样式
import './styles.less';

const { TabPane } = Tabs;

interface MySQLAnalysisProps {
  searchText: string;
}

const MySQLAnalysis: React.FC<MySQLAnalysisProps> = ({ searchText }) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('data');
  const [localSearchText, setLocalSearchText] = useState(searchText);
  const newTabIndex = useRef(0);
  const [refreshing, setRefreshing] = useState(false); // 新增：刷新状态
  
  // 趋势图表相关状态
  const [trendModalVisible, setTrendModalVisible] = useState(false);
  const [selectedDatabase, setSelectedDatabase] = useState<Database | null>(null);
  const [timeRange, setTimeRange] = useState<'7d' | '14d' | '30d'>('14d');
  const [trendData, setTrendData] = useState<TrendData[]>([]);
  const [dailyIncrementData, setDailyIncrementData] = useState<DailyIncrementData[]>([]);
  
  // 使用自定义hooks
  const { 
    loading, 
    databases, 
    filteredDatabases,
    fetchPrometheusData 
  } = usePrometheusData(localSearchText);
  
  const {
    aiLoading,
    aiAnalysisResult,
    analyzeWithDify,
    chatMessages,
    userQuestion,
    setUserQuestion,
    sendQuestion
  } = useAIAnalysis(filteredDatabases);
  
  const {
    trendData,
    dailyIncrementData,
    selectedDatabase,
    trendModalVisible,
    timeRange,
    setTrendModalVisible,
    setSelectedDatabase,
    openTrendChart,
    handleTimeRangeChange, // 使用 hook 中的函数
    generateTrendData,
    generateDailyIncrementData
  } = useTrendData();
  
  // 初始加载数据
  useEffect(() => {
    fetchPrometheusData();
  }, []);
  
  // 打开趋势图表
  // 打开趋势图表 - 修改为支持传入趋势数据和默认14天
  const openTrendChart = async (database: Database, initialTrendData?: any[], initialDailyData?: any[]) => {
    setSelectedDatabase(database);
    setTimeRange('14d'); // 默认设置为14天
    
    if (initialTrendData && initialDailyData) {
      // 如果传入了初始数据，直接使用
      setTrendData(initialTrendData);
      setDailyIncrementData(initialDailyData);
    } else {
      // 否则请求14天的数据
      try {
        const trendResponse = await getDatabaseOverviewTrendData({
          timeRange: '14d'
        });
        setTrendData(trendResponse.data.trendData);
        setDailyIncrementData(trendResponse.data.dailyIncrementData);
      } catch (error) {
        console.error('获取趋势数据失败:', error);
        // 如果接口失败，使用生成的数据作为备用
        setTrendData(generateTrendData(database, '14d'));
        setDailyIncrementData(generateDailyIncrementData(database, '14d'));
      }
    }
    
    setTrendModalVisible(true);
  };
  
  // 处理时间范围变化 - 修改为请求后端数据
  // 删除这个重复的函数定义
  // const handleTimeRangeChange = async (range: '7d' | '14d' | '30d') => {
  //   setTimeRange(range);
  //   
  //   if (selectedDatabase) {
  //     try {
  //       // 请求对应时间范围的数据
  //       const trendResponse = await getDatabaseOverviewTrendData({
  //         timeRange: range
  //       });
  //       setTrendData(trendResponse.data.trendData);
  //       setDailyIncrementData(trendResponse.data.dailyIncrementData);
  //     } catch (error) {
  //       console.error('获取趋势数据失败:', error);
  //       // 如果接口失败，使用生成的数据作为备用
  //       setTrendData(generateTrendData(selectedDatabase, range));
  //       setDailyIncrementData(generateDailyIncrementData(selectedDatabase, range));
  //     }
  //   }
  // };
  
  // 刷新所有数据 - 修改为先调用刷新API
  const refreshAllData = async () => {
    try {
      setRefreshing(true);
      
      // 1. 先调用刷新API，触发后端数据更新
      const refreshResponse = await refreshDiskAnalysisData();
      
      if (refreshResponse.success) {
        message.success('数据刷新成功');
        
        // 2. 等待一小段时间让后端处理完成
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 3. 重新获取所有数据
        await fetchPrometheusData();
        
        message.success('数据已更新');
      } else {
        throw new Error(refreshResponse.message || '刷新数据失败');
      }
    } catch (error) {
      console.error('刷新数据失败:', error);
      const errorMessage = error instanceof Error ? error.message : '刷新数据失败';
      message.error(errorMessage);
      
      // 即使刷新API失败，也尝试重新获取数据
      try {
        await fetchPrometheusData();
      } catch (fetchError) {
        console.error('重新获取数据失败:', fetchError);
      }
    } finally {
      setRefreshing(false);
    }
  };
  
  // 新增：添加详情Tab
  const addDetailTab = useCallback((databaseName: string) => {
    console.log('addDetailTab called with:', databaseName);
    const newActiveKey = `detail-${newTabIndex.current++}`;
    
    setItems((prevItems) => {
      // Function implementation...
    });
    
    setActiveTab(newActiveKey);
  }, [t, setItems, setActiveTab, newTabIndex]);
  
  // 新增：移除Tab
  const remove = (targetKey: string) => {
    let newActiveKey = activeTab;
    let lastIndex = -1;
    items.forEach((item, i) => {
      if (item.key === targetKey) {
        lastIndex = i - 1;
      }
    });
    const newPanes = items.filter((item) => item.key !== targetKey);
    if (newPanes.length && newActiveKey === targetKey) {
      if (lastIndex >= 0) {
        newActiveKey = newPanes[lastIndex].key;
      } else {
        newActiveKey = newPanes[0].key;
      }
    }
    setItems(newPanes);
    setActiveTab(newActiveKey);
  };

  const onEdit = (targetKey: React.MouseEvent | React.KeyboardEvent | string, action: 'add' | 'remove') => {
    if (action === 'remove') {
      remove(targetKey as string);
    }
  };
  
  const [items, setItems] = useState([
    {
      label: t('数据概览'),
      key: 'data',
      children: (
        <div>
<<<<<<< HEAD
=======
          <div className="mysql-refresh-section">
            <Button 
              type="primary" 
              icon={<SyncOutlined spin={refreshing || loading} />} 
              loading={refreshing || loading} 
              onClick={refreshAllData}
              size="small"
            >
              {refreshing ? t('刷新中...') : t('刷新数据')}
            </Button>
          </div>
          
>>>>>>> origin/master
          {/* 数据库概览表格 */}
          <DatabaseOverview 
            filteredDatabases={filteredDatabases} 
            loading={loading || refreshing}
            openTrendChart={openTrendChart}
          />
          
          {/* 数据库列表表格 */}
<<<<<<< HEAD
          <DatabaseList
=======
          <DatabaseList 
            filteredDatabases={filteredDatabases} 
            loading={loading || refreshing}
            localSearchText={localSearchText}
            setLocalSearchText={setLocalSearchText}
>>>>>>> origin/master
            openTrendChart={openTrendChart}
          />
          
          {/* 大文件列表表单 */}
          <LargeFilesList 
            loading={loading || refreshing}
            openTrendChart={openTrendChart}
          />
        </div>
      ),
    },
    {
      label: t('智能分析'),
      key: 'ai',
      children: (
        <AIAnalysisTab 
          aiLoading={aiLoading}
          aiAnalysisResult={aiAnalysisResult}
          analyzeWithDify={analyzeWithDify}
          trendData={trendData}
          chatMessages={chatMessages}
          userQuestion={userQuestion}
          setUserQuestion={setUserQuestion}
          sendQuestion={sendQuestion}
        />
      ),
    },
  ]);

  return (
    <div className="mysql-analysis-container">
      <div className="mysql-page-header">
        <div className="mysql-page-title">
          <DatabaseOutlined />
          {t('MySQL增长分析')}
        </div>
      </div>
      
      <Tabs
        type="editable-card"
        activeKey={activeTab}
        onChange={setActiveTab}
        onEdit={onEdit}
        items={items}
      />
      
      {/* 趋势图表模态框 */}
      <TrendModal 
        visible={trendModalVisible}
        onCancel={() => setTrendModalVisible(false)}
        selectedDatabase={selectedDatabase}
        trendData={trendData}
        dailyIncrementData={dailyIncrementData}
        timeRange={timeRange}
        handleTimeRangeChange={handleTimeRangeChange}
      />
    </div>
  );
};

export default MySQLAnalysis;