import { TimeRangeType, Database, LargeFile, DatabaseOverviewData } from './types';

// 文件类型颜色映射
export const FILE_TYPE_COLORS = {
  'ibdata': '#f50',
  'binlog': '#2db7f5',
  'table': '#87d068',
  'index': '#108ee9',
};

// 时间范围选项
export const TIME_RANGE_OPTIONS: { label: string; value: TimeRangeType }[] = [
  { label: '7日', value: '7d' },
  { label: '14日', value: '14d' },
  { label: '30日', value: '30d' },
];

// 趋势波动模式 - 用于生成模拟数据
export const TREND_PATTERNS = [
  1.02, 1.03, 1.01, 0.98, 0.97, 0.99, 1.01, // 一周的波动模式
  1.02, 0.96, 0.95, 1.03, 1.01, 0.98, 0.99  // 另一周的波动模式
];

// 模拟的Prometheus数据
export const MOCK_PROMETHEUS_DATA = {
  mysql_database_size: [
    { database: 'user_db', instance: 'mysql-prod-01', value: 125, timestamp: Date.now() },
    { database: 'order_db', instance: 'mysql-prod-02', value: 215, timestamp: Date.now() },
    { database: 'product_db', instance: 'mysql-prod-01', value: 178, timestamp: Date.now() },
  ]
};

// 模拟的数据库列表数据
// 模拟的数据库列表数据
export const MOCK_DATABASES: Database[] = [
  {
    id: 1,
    name: 'user_db',
    instance: 'mysql-prod-01',
    currentSize: 125,
    growthRate: 5.2,
    changeRate14Days: '15.8%',
    changeValue: 12.5,
    fileCount: 1500 // 添加文件数量
  },
  {
    id: 2,
    name: 'order_db',
    instance: 'mysql-prod-02',
    currentSize: 215,
    growthRate: 8.7,
    changeRate14Days: '18.2%',
    changeValue: 22.3,
    fileCount: 2300 // 添加文件数量
  },
  {
    id: 3,
    name: 'product_db',
    instance: 'mysql-prod-01',
    currentSize: 178,
    growthRate: 3.5,
    changeRate14Days: '10.5%',
    changeValue: 15.8,
    fileCount: 800 // 添加文件数量
  },
  {
    id: 4,
    name: 'log_db',
    instance: 'mysql-prod-03',
    currentSize: 320,
    growthRate: 12.3,
    changeRate14Days: '25.7%',
    changeValue: 45.2,
    fileCount: 5000 // 添加文件数量
  },
  {
    id: 5,
    name: 'analytics_db',
    instance: 'mysql-prod-02',
    currentSize: 420,
    growthRate: 7.8,
    changeRate14Days: '16.3%',
    changeValue: 38.5,
    fileCount: 3200 // 添加文件数量
  }
];

// 模拟的大文件列表数据
export const MOCK_LARGE_FILES: LargeFile[] = [
  {
    key: '1',
    fileName: 'user_data.ibd',
    filePath: '/var/lib/mysql/user_db/user_data.ibd',
    fileSize: '15.2 GB',
    fileType: 'table',
  },
  {
    key: '2',
    fileName: 'ibdata1',
    filePath: '/var/lib/mysql/ibdata1',
    fileSize: '12.8 GB',
    fileType: 'ibdata',
  },
  {
    key: '3',
    fileName: 'mysql-bin.000001',
    filePath: '/var/lib/mysql/mysql-bin.000001',
    fileSize: '8.5 GB',
    fileType: 'binlog',
  },
  {
    key: '4',
    fileName: 'order_history.ibd',
    filePath: '/var/lib/mysql/order_db/order_history.ibd',
    fileSize: '7.3 GB',
    fileType: 'table',
  },
  {
    key: '5',
    fileName: 'product_index.ibd',
    filePath: '/var/lib/mysql/product_db/product_index.ibd',
    fileSize: '5.9 GB',
    fileType: 'index',
  },
];

// 模拟的数据库概览数据
export const MOCK_DATABASE_OVERVIEW: DatabaseOverviewData = {
  key: '1',
  totalCount: MOCK_DATABASES.length,
  totalTables: 127,
  totalSize: `${MOCK_DATABASES.reduce((sum, db) => sum + db.currentSize, 0)} GB`,
  changeRate14Days: '15.8%',
  changeValue: 42.5
};

// 模拟的AI分析结果
export const MOCK_AI_ANALYSIS_RESULT = (databaseCount: number, userDbSize: number, orderDbSize: number, productDbSize: number) => `
## MySQL数据库增长分析报告

### 概述
分析了${databaseCount}个数据库的增长趋势，发现以下关键问题：

1. **user_db** 数据库当前大小为 **${userDbSize}GB**
2. **order_db** 数据库当前大小为 **${orderDbSize}GB**
3. **product_db** 数据库当前大小为 **${productDbSize}GB**

### 详细分析
各数据库的具体情况如下：
- user_db: 当前大小${userDbSize}GB
- order_db: 当前大小${orderDbSize}GB
- product_db: 当前大小${productDbSize}GB
`;

// 分页配置
export const PAGINATION_CONFIG = {
  pageSize: 8,
  showSizeChanger: true,
  showQuickJumper: true,
};

// 表格滚动配置
export const TABLE_SCROLL_CONFIG = { x: 1200 };

// 初始聊天消息
export const INITIAL_CHAT_MESSAGE = {
  role: 'ai' as const,
  content: '您好！我是MySQL增长分析助手，我可以帮您分析数据库增长趋势并提供优化建议。'
};