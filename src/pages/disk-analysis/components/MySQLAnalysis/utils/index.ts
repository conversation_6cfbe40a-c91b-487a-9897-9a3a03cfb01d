// 统一导出所有工具函数和数据
export * from './api';
export * from './dataTransformers';
export * from './mockData';

// 导出时间范围类型（从types.ts中提取）
export type TimeRangeType = '7d' | '14d' | '30d';

// 导出通用工具函数

/**
 * 格式化数字为带两位小数的字符串
 * @param value 要格式化的数字
 * @returns 格式化后的字符串
 */
export const formatNumber = (value: number): string => {
  return Math.round(value * 100) / 100 + '';
};

/**
 * 格式化日期为YYYY-MM-DD格式
 * @param date 日期对象
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: Date): string => {
  return date.toISOString().split('T')[0];
};

/**
 * 根据时间范围获取天数
 * @param range 时间范围
 * @returns 对应的天数
 */
export const getDaysFromRange = (range: TimeRangeType): number => {
  return range === '7d' ? 7 : range === '14d' ? 14 : 30;
};

/**
 * 计算增长率
 * @param currentValue 当前值
 * @param previousValue 先前值
 * @returns 增长率百分比
 */
export const calculateGrowthRate = (currentValue: number, previousValue: number): number => {
  if (previousValue === 0) return 0;
  return ((currentValue - previousValue) / previousValue) * 100;
};