import { Database, TrendData, DailyIncrementData } from '../types';
import { TimeRangeType } from './index';

/**
 * 生成趋势数据（确保数据精度一致）
 * @param database 数据库对象
 * @param range 时间范围
 * @returns 趋势数据数组
 */
export const generateTrendData = (database: Database, range: TimeRangeType = '14d'): TrendData[] => {
  // 这里应该是从Prometheus或其他数据源获取历史数据
  // 为了演示，我们生成模拟数据
  const now = new Date();
  const data: TrendData[] = [];
  
  // 根据选择的时间范围确定天数
  const pastDays = range === '7d' ? 7 : range === '14d' ? 14 : 30;
  
  // 基准大小和波动模式
  const baseSize = database.currentSize * 0.9; // 从当前大小的90%开始
  const patterns = [
    1.02, 1.03, 1.01, 0.98, 0.97, 0.99, 1.01, // 一周的波动模式
    1.02, 0.96, 0.95, 1.03, 1.01, 0.98, 0.99  // 另一周的波动模式
  ];
  
  // 生成过去的数据，添加明显的波动
  let currentSize = baseSize;
  for (let i = pastDays; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);
    const dateStr = date.toISOString().split('T')[0];
    
    // 使用波动模式生成数据
    const patternIndex = (pastDays - i) % patterns.length;
    const changeFactor = patterns[patternIndex];
    
    // 应用变化因子
    currentSize = currentSize * changeFactor;
    
    // 添加小的随机波动
    const randomFactor = 0.995 + Math.random() * 0.01; // 0.995到1.005之间的随机因子
    currentSize = currentSize * randomFactor;
    
    // 保存数据时保持精确值，仅在显示时四舍五入
    data.push({
      date: dateStr,
      size: Math.round(currentSize * 100) / 100, // 保留两位小数，使用Math.round确保精确计算
      database: database.name
    });
  }
  
  return data;
};

/**
 * 生成每日增量数据（确保与总量差值完全一致）
 * @param database 数据库对象
 * @param range 时间范围
 * @returns 每日增量数据数组
 */
export const generateDailyIncrementData = (database: Database, range: TimeRangeType = '14d'): DailyIncrementData[] => {
  // 首先获取总量数据
  const totalData = generateTrendData(database, range);
  const data: DailyIncrementData[] = [];
  
  // 计算每日增量（当前日期与前一天的差值）
  for (let i = 1; i < totalData.length; i++) {
    const currentDay = totalData[i];
    const previousDay = totalData[i-1];
    
    // 直接计算差值，确保与总量数据完全一致
    const increment = Number((currentDay.size - previousDay.size).toFixed(2));
    
    data.push({
      date: currentDay.date,
      increment: increment, // 使用精确的差值，不进行额外处理
      database: database.name
    });
  }
  
  return data;
};

/**
 * 过滤数据库列表
 * @param databases 数据库列表
 * @param searchText 搜索文本
 * @returns 过滤后的数据库列表
 */
export const filterDatabases = (databases: Database[], searchText: string): Database[] => {
  if (!searchText) return databases;
  
  return databases.filter(item => 
    item.name.toLowerCase().includes(searchText.toLowerCase()) || 
    item.instance.toLowerCase().includes(searchText.toLowerCase())
  );
};

/**
 * 计算数据库概览数据
 * @param databases 数据库列表
 * @returns 数据库概览数据
 */
export const calculateDatabaseOverview = (databases: Database[]) => {
  return {
    key: '1',
    totalCount: databases.length,
    totalTables: 127, // 这里添加了总表数量，可以根据实际数据修改
    totalSize: `${databases.reduce((sum, db) => sum + db.currentSize, 0)} GB`,
    changeRate14Days: '15.8%',
    changeValue: 42.5 // 14日内增长的具体数值（GB）
  };
};

/**
 * 生成总体趋势数据
 * @param databases 数据库列表
 * @returns 总体趋势数据库对象
 */
export const generateOverallTrendData = (databases: Database[]): Database => {
  return {
    id: 0,
    name: '所有数据库',
    instance: '全部实例',
    currentSize: databases.reduce((sum, db) => sum + db.currentSize, 0),
    growthRate: databases.reduce((sum, db) => sum + (db.growthRate || 0), 0) / databases.length,
  };
};