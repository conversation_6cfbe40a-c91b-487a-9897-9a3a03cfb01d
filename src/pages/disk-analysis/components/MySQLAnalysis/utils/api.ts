import { Database, ChatMessage } from '../types';

/**
 * 从Prometheus获取MySQL数据库大小数据
 * @returns Promise<any> 包含数据库大小信息的Promise
 */
export const fetchPrometheusData = async (): Promise<any> => {
  try {
    // 这里应该是实际从Prometheus API获取数据的代码
    // 例如：const response = await fetch('/api/prometheus/query?query=mysql_database_size');
    
    // 模拟API请求延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 模拟从Prometheus获取的数据
    const mockPrometheusData = {
      mysql_database_size: [
        { database: 'user_db', instance: 'mysql-prod-01', value: 125, timestamp: Date.now() },
        { database: 'order_db', instance: 'mysql-prod-02', value: 215, timestamp: Date.now() },
        { database: 'product_db', instance: 'mysql-prod-01', value: 178, timestamp: Date.now() },
      ]
    };
    
    return mockPrometheusData;
  } catch (error) {
    console.error('从Prometheus获取数据失败:', error);
    throw error;
  }
};

/**
 * 使用Dify API分析数据库数据
 * @param databases 数据库列表
 * @param prometheusMetrics Prometheus指标数据
 * @returns Promise<string> 包含分析结果的Promise
 */
export const analyzeWithDify = async (databases: Database[], prometheusMetrics: any): Promise<string> => {
  try {
    // 这里应该是实际调用Dify API的代码
    // 准备发送给Dify的数据
    const dataForAnalysis = {
      databases,
      prometheusMetrics,
      timeRange: '最近30天'
    };
    
    // 模拟API请求延迟
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 模拟Dify返回的分析结果
    const mockAnalysisResult = `
## MySQL数据库增长分析报告

### 概述
分析了${databases.length}个数据库的增长趋势，发现以下关键问题：

1. **user_db** 数据库当前大小为 **${databases.find(db => db.name === 'user_db')?.currentSize}GB**
2. **order_db** 数据库当前大小为 **${databases.find(db => db.name === 'order_db')?.currentSize}GB**
3. **product_db** 数据库当前大小为 **${databases.find(db => db.name === 'product_db')?.currentSize}GB**

### 详细分析
各数据库的具体情况如下：
- user_db: 当前大小${databases.find(db => db.name === 'user_db')?.currentSize}GB
- order_db: 当前大小${databases.find(db => db.name === 'order_db')?.currentSize}GB
- product_db: 当前大小${databases.find(db => db.name === 'product_db')?.currentSize}GB
    `;
    
    return mockAnalysisResult;
  } catch (error) {
    console.error('调用Dify API分析失败:', error);
    throw error;
  }
};

/**
 * 发送用户问题到AI助手
 * @param question 用户问题
 * @param databases 数据库列表
 * @param prometheusMetrics Prometheus指标数据
 * @returns Promise<string> 包含AI回答的Promise
 */
export const sendQuestionToAI = async (
  question: string,
  databases: Database[],
  prometheusMetrics: any
): Promise<string> => {
  try {
    // 这里应该是实际调用Dify API的代码
    // 准备发送给Dify的数据
    const questionData = {
      question,
      context: {
        databases,
        prometheusMetrics
      }
    };
    
    // 模拟API请求延迟
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // 模拟Dify返回的回答
    const aiResponse = `感谢您的问题。根据当前数据分析，我们有${databases.length}个数据库，总容量为${databases.reduce((sum, db) => sum + db.currentSize, 0)}GB。您可以查看详细的分析报告获取更多信息。`;
    
    return aiResponse;
  } catch (error) {
    console.error('调用Dify API对话失败:', error);
    throw error;
  }
};

/**
 * 更新聊天消息列表
 * @param chatMessages 当前聊天消息列表
 * @param userQuestion 用户问题
 * @param aiResponse AI回答
 * @returns 更新后的聊天消息列表
 */
export const updateChatMessages = (
  chatMessages: ChatMessage[],
  userQuestion: string,
  aiResponse: string
): ChatMessage[] => {
  return [
    ...chatMessages,
    { role: 'user', content: userQuestion },
    { role: 'ai', content: aiResponse }
  ];
};