import { Database, LargeFile } from '../types';

// MySQL数据库模拟数据
export const mockDatabases: Database[] = [
  {
    id: 1,
    name: 'user_db',
    instance: 'mysql-prod-01',
    currentSize: 120,
    growthRate: 15,
    changeRate14Days: '15.8%',
    changeValue: 12.5,
    fileCount: 1500 // 模拟文件数量
  },
  {
    id: 2,
    name: 'order_db',
    instance: 'mysql-prod-02',
    currentSize: 250,
    growthRate: 8,
    changeRate14Days: '8.2%',
    changeValue: 18.9,
    fileCount: 2300 // 模拟文件数量
  },
  {
    id: 3,
    name: 'product_db',
    instance: 'mysql-prod-01',
    currentSize: 80,
    growthRate: 5,
    changeRate14Days: '5.5%',
    changeValue: 4.2,
    fileCount: 800 // 模拟文件数量
  },
  {
    id: 4,
    name: 'log_db',
    instance: 'mysql-prod-03',
    currentSize: 500,
    growthRate: 25,
    changeRate14Days: '24.7%',
    changeValue: 98.5,
    fileCount: 5000 // 模拟文件数量
  },
  {
    id: 5,
    name: 'analytics_db',
    instance: 'mysql-prod-04',
    currentSize: 350,
    growthRate: 12,
    changeRate14Days: '12.3%',
    changeValue: 38.4,
    fileCount: 3200 // 模拟文件数量
  },
];

// 大文件列表模拟数据
export const mockLargeFiles: LargeFile[] = [
  {
    key: '1',
    fileName: 'user_data.ibd',
    filePath: '/var/lib/mysql/user_db/user_data.ibd',
    fileSize: '15.2 GB',
    fileType: 'table',
    databaseName: 'user_db',
    lastModified: '2023-10-15'
  },
  {
    key: '2',
    fileName: 'ibdata1',
    filePath: '/var/lib/mysql/ibdata1',
    fileSize: '12.8 GB',
    fileType: 'ibdata',
    databaseName: 'system',
    lastModified: '2023-10-18'
  },
  {
    key: '3',
    fileName: 'mysql-bin.000001',
    filePath: '/var/lib/mysql/mysql-bin.000001',
    fileSize: '8.5 GB',
    fileType: 'binlog',
    databaseName: 'system',
    lastModified: '2023-10-20'
  },
  {
    key: '4',
    fileName: 'order_history.ibd',
    filePath: '/var/lib/mysql/order_db/order_history.ibd',
    fileSize: '7.3 GB',
    fileType: 'table',
    databaseName: 'order_db',
    lastModified: '2023-10-17'
  },
  {
    key: '5',
    fileName: 'product_index.ibd',
    filePath: '/var/lib/mysql/product_db/product_index.ibd',
    fileSize: '5.9 GB',
    fileType: 'index',
    databaseName: 'product_db',
    lastModified: '2023-10-19'
  },
];

// 初始聊天消息
export const initialChatMessages = [
  {role: 'ai', content: '您好！我是MySQL增长分析助手，我可以帮您分析数据库增长趋势并提供优化建议。'}
];

// 模拟Prometheus数据结构
export interface MockPrometheusData {
  mysql_database_size: {
    database: string;
    instance: string;
    value: number;
    timestamp: number;
  }[];
}

// 模拟Prometheus数据
export const mockPrometheusData: MockPrometheusData = {
  mysql_database_size: [
    { database: 'user_db', instance: 'mysql-prod-01', value: 125, timestamp: Date.now() },
    { database: 'order_db', instance: 'mysql-prod-02', value: 215, timestamp: Date.now() },
    { database: 'product_db', instance: 'mysql-prod-01', value: 178, timestamp: Date.now() },
    { database: 'log_db', instance: 'mysql-prod-03', value: 510, timestamp: Date.now() },
    { database: 'analytics_db', instance: 'mysql-prod-04', value: 362, timestamp: Date.now() },
  ]
};