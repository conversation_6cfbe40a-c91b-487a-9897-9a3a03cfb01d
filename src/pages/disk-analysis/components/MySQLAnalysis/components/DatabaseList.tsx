import React, { useState, useEffect } from 'react';
import { Table, Space, Button, Card, Input, Empty, message } from 'antd';
import { LineChartOutlined, InfoCircleOutlined, SearchOutlined, SyncOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useHistory } from 'react-router-dom';
import { DatabaseListProps, Database } from '../types';
import { getDatabaseTrendData, getDatabaseList } from '../../../../../services/diskAnalysisService';

const DatabaseList: React.FC<DatabaseListProps> = ({
  openTrendChart
}) => {
  const { t } = useTranslation();
  const history = useHistory();
  
  const [localSearchText, setLocalSearchText] = useState('');
  const [databases, setDatabases] = useState<Database[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取数据库列表数据
  const fetchDatabases = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await getDatabaseList({
        page: 1,
        pageSize: 100,
        search: '',
      });
      
      if (response.success && response.data) {
        const { list } = response.data;
        
        // 转换API数据为组件所需的Database格式
        const transformedDatabases: Database[] = list.map((item: any) => ({
          id: item.id || item.name,
          name: item.name,
          instance: item.instance || 'localhost:3306',
          currentSize: item.currentSize || 0,
          tableCount: item.tableCount || 0,
          fileCount: item.fileCount || 0,
          changeRate14Days: item.changeRate14Days || '0%',
          changeValue: item.changeValue || 0,
          // 添加7日和30日变化率和变化值
          changeRate7Days: item.changeRate7Days || '0%',
          changeValue7Days: item.changeValue7Days || 0,
          changeRate30Days: item.changeRate30Days || '0%',
          changeValue30Days: item.changeValue30Days || 0,
          lastUpdated: item.lastUpdated || new Date().toISOString(),
          status: item.status || 'active'
        }));
        
        setDatabases(transformedDatabases);
      } else {
        throw new Error(response.message || '获取数据库列表失败');
      }
    } catch (err) {
      console.error('获取数据库列表失败:', err);
      const errorMessage = err instanceof Error ? err.message : '获取数据库列表失败';
      setError(errorMessage);
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchDatabases();
  }, []);

  // 过滤数据库列表
  const filteredDatabases = databases.filter(db =>
    db.name.toLowerCase().includes(localSearchText.toLowerCase()) ||
    db.instance.toLowerCase().includes(localSearchText.toLowerCase())
  );
  
  // 修改详情按钮点击处理函数
  const handleDetailClick = (record: Database) => {
    console.log('点击详情按钮，数据库信息:', record);
    // 跳转到详情页面，传递完整的数据库对象
    history.push('/monitor/disk-analysis/database-detail', {
      databaseInfo: record,
    });
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: t('数据库名称'),
      dataIndex: 'name',
      key: 'name',
      width: 160,
    },
    {
      title: t('实例'),
      dataIndex: 'instance',
      key: 'instance',
      width: 140,
    },
    {
      title: t('文件数量'),
      dataIndex: 'fileCount',
      key: 'fileCount',
      width: 110,
    },
    {
      title: t('当前大小'),
      dataIndex: 'currentSize',
      key: 'currentSize',
      width: 130,
      sorter: (a, b) => {
        // 解析文件大小进行排序（假设格式为 "15.2 GB"）
        const parseSize = (size: string) => {
          const match = size.match(/([\d\.]+)\s*(\w+)/);
          if (!match) return 0;
          const value = parseFloat(match[1]);
          const unit = match[2].toUpperCase();
          const multipliers = { 'B': 1, 'KB': 1024, 'MB': 1024**2, 'GB': 1024**3, 'TB': 1024**4 };
          return value * (multipliers[unit] || 1);
        };
        return parseSize(a.currentSize) - parseSize(b.currentSize);
      },
      render: (text) => (
        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>
          {text}
        </span>
      ),
    },
    {
      title: t('近14日变化率'),
      dataIndex: 'changeRate14Days',
      key: 'changeRate14Days',
      width: 130,
      render: (text, record) => (
        <div>
          <span style={{ color: record.changeValue > 0 ? '#f5222d' : '#52c41a' }}>
            {record.changeValue > 0 ? '+' : ''}{text}
          </span>
          <span style={{ marginLeft: 8, fontSize: '12px', color: '#8c8c8c' }}>
            ({record.changeValue > 0 ? '+' : ''}{record.changeValue.toFixed(2)} GB)
          </span>
        </div>
      ),
    },
    {
      title: t('操作'),
      key: 'action',
      width: 180,
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="link" 
            icon={<LineChartOutlined />} 
            size="small"
            onClick={async () => {
              try {
                // 调用后端接口获取单个数据库的趋势数据
                const trendResponse = await getDatabaseTrendData({
                  databaseName: record.name,
                  timeRange: '14d' // 默认14天
                });
                
                // 直接使用已有的数据，不需要再次请求接口
                const completeRecord = {
                  ...record,
                  // 确保使用 API 返回的数据
                  changeRate7Days: record.changeRate7Days || '0%',
                  changeValue7Days: record.changeValue7Days || 0,
                  changeRate30Days: record.changeRate30Days || '0%',
                  changeValue30Days: record.changeValue30Days || 0
                };
                
                // 调用父组件传递的openTrendChart函数，传递完整的数据库信息和趋势数据
                openTrendChart(completeRecord, trendResponse.data.trendData, trendResponse.data.dailyIncrementData);
              } catch (error) {
                console.error('获取数据库趋势数据失败:', error);
                message.error('获取趋势数据失败，请稍后重试');
              }
            }}
          >
            {t('变化趋势')}
          </Button>
          <Button 
            type="link" 
            icon={<InfoCircleOutlined />} 
            size="small"
            onClick={() => handleDetailClick(record)}
          >
            {t('详情')}
          </Button>
        </Space>
      ),
    },
  ];

  // 检查是否有搜索结果
  const hasSearchResults = filteredDatabases.length > 0;
  const isSearching = localSearchText.trim().length > 0;
  const hasData = databases.length > 0;

  return (
    <Card 
      title={<span style={{ fontSize: '16px' }}>{t('数据库列表')}</span>}
      extra={
        <Space>
          <Button 
            type="primary"
            size="small" 
            icon={<SyncOutlined />}
            onClick={fetchDatabases}
            loading={loading}
          >
            {t('刷新')}
          </Button>
          <Input
            placeholder={t('搜索数据库名称或实例')}
            prefix={<SearchOutlined />}
            value={localSearchText}
            onChange={e => setLocalSearchText(e.target.value)}
            style={{ width: 180 }}
            allowClear
            size='small'
          />
        </Space>
      }
    >
      {error && (
        <div style={{ marginBottom: 16, padding: 8, background: '#fff2f0', border: '1px solid #ffccc7', borderRadius: 4 }}>
          <span style={{ color: '#ff4d4f' }}>错误: {error}</span>
          <Button type="link" size="small" onClick={fetchDatabases}>
            重试
          </Button>
        </div>
      )}
      
      <Table
        columns={columns}
        dataSource={filteredDatabases}
        loading={loading}
        rowKey="name"
        pagination={{
          pageSize: 8,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} / ${total} ${t('条记录')}`,
        }}
        scroll={{ x: 1200 }}
        size="small"
        locale={{
          emptyText: (
            <Empty 
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                error ? (
                  <span style={{ color: '#8c8c8c' }}>
                    {t('数据加载失败')}<br/>
                    <Button type="link" size="small" onClick={fetchDatabases}>
                      {t('点击重试')}
                    </Button>
                  </span>
                ) : isSearching ? (
                  <span style={{ color: '#8c8c8c' }}>
                    {t('未找到匹配的数据库')}<br/>
                    <span style={{ fontSize: '12px' }}>
                      {t('尝试修改搜索条件或')}
                      <Button 
                        type="link" 
                        size="small" 
                        style={{ padding: 0, fontSize: '12px' }}
                        onClick={() => setLocalSearchText('')}
                      >
                        {t('清空搜索')}
                      </Button>
                    </span>
                  </span>
                ) : !hasData ? (
                  <span style={{ color: '#8c8c8c' }}>
                    {t('暂无数据库数据')}<br/>
                    <span style={{ fontSize: '12px' }}>{t('请检查数据源连接或稍后重试')}</span>
                  </span>
                ) : (
                  <span style={{ color: '#8c8c8c' }}>
                    {t('暂无数据')}
                  </span>
                )
              }
            />
          )
        }}
      />
    </Card>
  );
};

export default DatabaseList;