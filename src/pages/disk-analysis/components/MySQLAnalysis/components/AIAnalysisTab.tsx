import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Row, Col, Spin, Empty, Input } from 'antd';
import { RobotOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { Line } from '@ant-design/plots';
import { AIAnalysisTabProps } from '../types';

const { TextArea } = Input;

const AIAnalysisTab: React.FC<AIAnalysisTabProps> = ({
  aiLoading,
  aiAnalysisResult,
  analyzeWithDify,
  trendData,
  chatMessages,
  userQuestion,
  setUserQuestion,
  sendQuestion
}) => {
  const { t } = useTranslation();

  return (
    <div className="mysql-ai-analysis">
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'flex-end' }}>
        <Button 
          type="primary" 
          icon={<RobotOutlined />} 
          onClick={analyzeWithDify}
          loading={aiLoading}
        >
          {t('AI分析数据')}
        </Button>
      </div>
      <Row gutter={16}>
        <Col span={24}>
          <Card title={t('AI分析报告')} className="mysql-ai-report-card">
            {aiLoading ? (
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <Spin size="large" />
                <p style={{ marginTop: 16 }}>{t('AI正在分析数据，请稍候...')}</p>
              </div>
            ) : aiAnalysisResult ? (
              <div className="mysql-ai-report-content" style={{ whiteSpace: 'pre-wrap' }}>
                {aiAnalysisResult}
              </div>
            ) : (
              <Empty description={t('暂无分析报告，请点击"AI分析数据"按钮生成报告')} />
            )}
          </Card>
          
          {/* 增长趋势部分 - 从独立标签页移到AI分析页面中 */}
          <Card title={t('增长趋势')} style={{ marginTop: 16 }} className="mysql-trends-card">
            {trendData.length > 0 ? (
              <Line
                data={trendData}
                xField="date"
                yField="size"
                seriesField="database"
                xAxis={{
                  title: {
                    text: t('日期'),
                  },
                }}
                yAxis={{
                  title: {
                    text: t('大小 (GB)'),
                  },
                }}
                tooltip={{
                  formatter: (datum) => {
                    return { name: datum.database, value: `${datum.size} GB` };
                  },
                }}
                annotations={[
                  // 添加当前日期的垂直线
                  {
                    type: 'line',
                    start: [new Date().toISOString().split('T')[0], 'min'],
                    end: [new Date().toISOString().split('T')[0], 'max'],
                    style: {
                      stroke: '#1890ff',
                      lineDash: [3, 3],
                    },
                  },
                ]}
              />
            ) : (
              <div>
                <Empty description={t('请点击数据库列表中的"变化趋势"按钮查看详细趋势')} />
              </div>
            )}
          </Card>
        </Col>
        <Col span={8} style={{ display: 'none' }}>
          <Card title={t('MySQL增长分析助手')} className="mysql-ai-chat-card">
            <div className="mysql-ai-chat-messages" style={{ height: 300, overflowY: 'auto', marginBottom: 16 }}>
              {chatMessages.map((msg, index) => (
                <div key={index} className={`mysql-ai-message ${msg.role}`} style={{
                  padding: '8px 12px',
                  marginBottom: 8,
                  borderRadius: 6,
                  maxWidth: '85%',
                  backgroundColor: msg.role === 'ai' ? '#f0f2f5' : '#e6f7ff',
                  marginLeft: msg.role === 'ai' ? 0 : 'auto',
                  marginRight: msg.role === 'ai' ? 'auto' : 0,
                }}>
                  <p style={{ margin: 0 }}>{msg.content}</p>
                </div>
              ))}
            </div>
            <div className="mysql-ai-chat-input">
              <TextArea 
                rows={3} 
                value={userQuestion}
                onChange={e => setUserQuestion(e.target.value)}
                placeholder={t('输入您的问题，例如：如何优化数据库增长？')}
                disabled={aiLoading}
              />
              <Button 
                type="primary" 
                onClick={sendQuestion} 
                loading={aiLoading}
                disabled={!userQuestion.trim()}
                style={{ marginTop: 8, float: 'right' }}
              >
                {t('发送')}
              </Button>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default AIAnalysisTab;