import React, { useState, useEffect } from 'react';
import { Table, Space, Button, Card, Alert, Empty } from 'antd';
import { LineChartOutlined, SyncOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { DatabaseOverviewProps, Database } from '../types';
import { getDatabaseOverview, getDatabaseOverviewTrendData } from '../../../../../services/diskAnalysisService';
import { message } from 'antd';

/**
 * 数据库概览组件
 * 显示数据库总数、总表数量、总存储容量和变化率等统计信息
 */
const DatabaseOverview: React.FC<DatabaseOverviewProps> = ({
  filteredDatabases,
  loading,
  openTrendChart
}) => {
  const { t } = useTranslation();
  const [overviewData, setOverviewData] = useState<any>(null);
  const [overviewLoading, setOverviewLoading] = useState(false);
  const [error, setError] = useState<string>();

  // 获取概览数据
  const fetchOverviewData = async () => {
    setOverviewLoading(true);
    setError(undefined);
    
    try {
      const response = await getDatabaseOverview();
      // 修改判断逻辑：code === 0 表示成功
      if (response.code === 0 && response.data) {
        setOverviewData(response.data);
        message.success('概览数据刷新成功');
      } else {
        setError(response.message || '获取概览数据失败');
      }
    } catch (error: any) {
      console.error('获取数据库概览失败:', error);
      setError(error.message || '网络请求失败');
    } finally {
      setOverviewLoading(false);
    }
  };

  useEffect(() => {
    fetchOverviewData();
  }, []);

  // 计算总存储容量（从过滤后的数据库列表）
  const totalSize = filteredDatabases.reduce((sum, db) => sum + db.currentSize, 0);
  
  // 计算总表数量（从过滤后的数据库列表）
  const totalTables = filteredDatabases.reduce((sum, db) => sum + (db.tableCount || 0), 0);
  
  // 计算平均增长率
  const avgGrowthRate = filteredDatabases.length > 0
    ? filteredDatabases.reduce((sum, db) => sum + (db.growthRate || 0), 0) / filteredDatabases.length
    : 0;

  // 计算7日内变化值和变化率
  const totalChangeValue7Days = filteredDatabases.reduce((sum, db) => sum + (db.changeValue7Days || 0), 0);
  const overallChangeRate7Days = totalSize > 0 ? ((totalChangeValue7Days / (totalSize - totalChangeValue7Days)) * 100).toFixed(1) : '0.0';

  // 计算14日内变化值（保持原有逻辑）
  const totalChangeValue = filteredDatabases.reduce((sum, db) => sum + (db.changeValue || 0), 0);
  const overallChangeRate = totalSize > 0 ? ((totalChangeValue / (totalSize - totalChangeValue)) * 100).toFixed(1) : '0.0';

  // 计算30日内变化值和变化率
  const totalChangeValue30Days = filteredDatabases.reduce((sum, db) => sum + (db.changeValue30Days || 0), 0);
  const overallChangeRate30Days = totalSize > 0 ? ((totalChangeValue30Days / (totalSize - totalChangeValue30Days)) * 100).toFixed(1) : '0.0';

  // 如果有API数据，优先使用API数据，否则使用计算数据
  const displayData = {
    totalCount: overviewData?.totalDatabases || filteredDatabases.length,
    totalTables: overviewData?.totalTables || totalTables,
    totalSize: overviewData?.totalSize || totalSize,
    changeRate7Days: overviewData?.changeRate7Days || `${overallChangeRate7Days}%`,
    changeValue7Days: overviewData?.changeValue7Days || totalChangeValue7Days,
    changeRate14Days: overviewData?.changeRate14Days || `${overallChangeRate}%`,
    changeValue: overviewData?.changeValue14Days || totalChangeValue,
    changeRate30Days: overviewData?.changeRate30Days || `${overallChangeRate30Days}%`,
    changeValue30Days: overviewData?.changeValue30Days || totalChangeValue30Days,
    version: overviewData?.version || 'MySQL 8.0.33',
    path: overviewData?.path || '/var/lib/mysql',
    binlogEnabled: overviewData?.binlogEnabled || false,  // 添加binlog状态
    binlogTotalSize: overviewData?.binlogTotalSize || 0   // 添加binlog总大小
  };

  // 检查是否有数据
  const hasData = filteredDatabases.length > 0 || overviewData;
  const isLoading = loading || overviewLoading;

  return (
    <Card 
      title={<span style={{ fontSize: '16px' }}>{t('数据库概览')}</span>} 
      style={{ marginBottom: 16 }}
      extra={
        <Space>
          <Button 
            type="primary"
            size="small" 
            icon={<SyncOutlined />}
            onClick={fetchOverviewData}
            loading={overviewLoading}
          >
            {t('刷新')}
          </Button>
          {error && (
            <Button 
              type="link" 
              size="small" 
              onClick={fetchOverviewData}
              loading={overviewLoading}
            >
              重试
            </Button>
          )}
        </Space>
      }
    >
      {error && (
        <Alert
          message="概览数据获取失败"
          description={error}
          type="warning"
          showIcon
            style={{ marginBottom: 16 }}
            action={
              <Button size="small" onClick={fetchOverviewData} loading={overviewLoading}>
                重试
              </Button>
            }
          />
        )}
        
        {!isLoading && !hasData && !error ? (
        <Empty 
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            <span style={{ color: '#8c8c8c' }}>
              {t('暂无数据库数据')}<br/>
              <span style={{ fontSize: '12px' }}>{t('请检查数据源连接或稍后重试')}</span>
            </span>
          }
        />
      ) : (
        <Table 
          columns={[
            {
              title: t('版本号'),
              dataIndex: 'version',
              key: 'version',
              render: (text) => (
                <span style={{ fontFamily: 'monospace', color: '#1890ff' }}>
                  {text}
                </span>
              ),
            },
            {
              title: t('路径'),
              dataIndex: 'path',
              key: 'path',
              render: (text) => (
                <span style={{ fontFamily: 'monospace', fontSize: '12px', color: '#666' }}>
                  {text}
                </span>
              ),
            },
            {
              title: t('总数据库数量'),
              dataIndex: 'totalCount',
              key: 'totalCount',
            },
            {
              title: t('总表数量'),
              dataIndex: 'totalTables',
              key: 'totalTables',
            },
            {
              title: t('总存储容量'),
              dataIndex: 'totalSize',
              key: 'totalSize',
              render: (value) => `${typeof value === 'number' ? value.toFixed(2) : value}`
            },
            
            // 保留这一列：近14日变化率
            {
              title: t('近14日变化率'),
              dataIndex: 'changeRate14Days',
              key: 'changeRate14Days',
              render: (text, record) => (
                <div>
                  <span style={{ color: record.changeValue > 0 ? '#f5222d' : '#52c41a' }}>
                    {record.changeValue > 0 ? '+' : ''}{record.changeRate14Days}
                  </span>
                  <span style={{ marginLeft: 8, fontSize: '12px', color: '#8c8c8c' }}>
                    ({record.changeValue > 0 ? '+' : ''}{typeof record.changeValue === 'number' ? record.changeValue.toFixed(2) : record.changeValue} GB)
                  </span>
                </div>
              ),
            },

            {
              title: t('binlog状态'),
              dataIndex: 'binlogEnabled',
              key: 'binlogEnabled',
              render: (enabled) => (
                <span style={{ color: enabled ? '#52c41a' : '#f5222d' }}>
                  {enabled ? t('已开启') : t('未开启')}
                </span>
              ),
            },
            {
              title: t('binlog总量'),
              dataIndex: 'binlogTotalSize',
              key: 'binlogTotalSize',
              render: (size) => `${typeof size === 'number' ? size.toFixed(2) : '0.00'} GB`,
            },
            {
              title: t('操作'),
              key: 'action',
              render: () => (
                <Space size="middle">
                  <Button 
                    type="link" 
                    icon={<LineChartOutlined />}
                    disabled={!hasData}
                    // 在变化趋势按钮的点击事件中
                    onClick={async () => {
                      if (hasData) {
                        try {
                          // 调用后端接口获取趋势数据
                          const trendResponse = await getDatabaseOverviewTrendData({
                            timeRange: '14d' // 默认14天
                          });
                          
                          // 使用现有的 displayData 数据，只传递图表数据
                          const overallData: Database = {
                            id: 0,
                            name: '所有数据库',  // 特殊标记，用于在useTrendData中区分
                            instance: '全部实例',
                            currentSize: displayData.totalSize,
                            growthRate: avgGrowthRate,
                            tableCount: displayData.totalTables,
                            fileCount: 0,
                            changeRate7Days: displayData.changeRate7Days,
                            changeValue7Days: displayData.changeValue7Days,
                            changeRate14Days: displayData.changeRate14Days,
                            changeValue: displayData.changeValue,
                            changeRate30Days: displayData.changeRate30Days,
                            changeValue30Days: displayData.changeValue30Days,
                            lastUpdated: new Date().toISOString(),
                            status: 'active'
                          };
                          
                          // 只保留这一次调用，删除上面的重复调用
                          openTrendChart(overallData, trendResponse.data.trendData, trendResponse.data.dailyIncrementData);
                        } catch (error) {
                          console.error('获取趋势数据失败:', error);
                          message.error('获取趋势数据失败，请稍后重试');
                        }
                      }
                    }}
                  >
                    {t('变化趋势')}
                  </Button>
                </Space>
              ),
            }
          ]} 
          dataSource={hasData ? [{
            key: '1',
            totalCount: displayData.totalCount,
            totalTables: displayData.totalTables,
            totalSize: displayData.totalSize,
            changeRate7Days: displayData.changeRate7Days,
            changeValue7Days: displayData.changeValue7Days,
            changeRate14Days: displayData.changeRate14Days,
            changeValue: displayData.changeValue,
            changeRate30Days: displayData.changeRate30Days,
            changeValue30Days: displayData.changeValue30Days,
            version: displayData.version,
            path: displayData.path,
            binlogEnabled: displayData.binlogEnabled,
            binlogTotalSize: displayData.binlogTotalSize
          }] : []}
          pagination={false}
          showHeader={true}
          loading={isLoading}
          locale={{
            emptyText: (
              <Empty 
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={t('暂无概览数据')}
              />
            )
          }}
        />
      )}
    </Card>
  );
};

export default DatabaseOverview;