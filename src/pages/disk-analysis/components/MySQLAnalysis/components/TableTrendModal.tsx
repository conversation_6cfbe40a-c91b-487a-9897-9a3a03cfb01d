import React, { useState, useEffect } from 'react';
import { Modal, Card, Row, Col, Radio, message } from 'antd';
import { Line, Column } from '@ant-design/plots';
import { useTranslation } from 'react-i18next';
import { getDatabaseTableTrendData } from '../../../../../services/diskAnalysisService';

interface TableTrendModalProps {
  visible: boolean;
  onClose: () => void;
  databaseName: string;
  tableName: string;
}

interface TrendData {
  date: string;
  size: number;
  database: string;
}

interface DailyIncrementData {
  date: string;
  increment: number;
  database: string;
}

type TimeRangeType = '7d' | '14d' | '30d';

const TableTrendModal: React.FC<TableTrendModalProps> = ({
  visible,
  onClose,
  databaseName,
  tableName
}) => {
  const { t } = useTranslation();
  const [timeRange, setTimeRange] = useState<TimeRangeType>('14d');
  const [trendData, setTrendData] = useState<TrendData[]>([]);
  const [dailyIncrementData, setDailyIncrementData] = useState<DailyIncrementData[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentSize, setCurrentSize] = useState<string>('0 GB');

  // 添加调试日志
  console.log('=== TableTrendModal 渲染信息 ===');
  console.log('visible:', visible);
  console.log('databaseName:', databaseName);
  console.log('tableName:', tableName);
  console.log('timeRange:', timeRange);
  console.log('================================');

  // 处理时间范围变更
  const handleTimeRangeChange = (range: TimeRangeType) => {
    console.log('时间范围变更为:', range);
    setTimeRange(range);
    fetchTrendData(range);
  };

  // 生成模拟趋势数据
  const generateMockTrendData = (range: TimeRangeType) => {
    const days = range === '7d' ? 7 : range === '14d' ? 14 : 30;
    const trendData: TrendData[] = [];
    const dailyIncrementData: DailyIncrementData[] = [];
    const baseSize = 100; // 基础大小 MB
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      const variation = Math.random() * 0.2 - 0.1; // -10% 到 +10% 的变化
      const size = baseSize * (1 + variation * (days - i) / days);
      
      trendData.push({
        date: dateStr,
        size: Math.round(size * 100) / 100,
        database: `${databaseName}.${tableName}`
      });
      
      if (i < days - 1) {
        const prevSize = trendData[trendData.length - 2].size;
        const currSize = trendData[trendData.length - 1].size;
        const increment = currSize - prevSize;
        
        dailyIncrementData.push({
          date: dateStr,
          increment: Math.round(increment * 100) / 100,
          database: `${databaseName}.${tableName}`
        });
      }
    }
    
    return { trendData, dailyIncrementData };
  };

  // 获取趋势数据
  const fetchTrendData = async (range: TimeRangeType) => {
    console.log('开始获取趋势数据:', { databaseName, tableName, range });
    setLoading(true);
    try {
      const response = await getDatabaseTableTrendData({
        databaseName,
        tableName,
        timeRange: range
      });
      
      console.log('API响应:', response);
      
      const responseData = response.data || response;
      setTrendData(responseData.trendData || []);
      setDailyIncrementData(responseData.dailyIncrementData || []);
      
      // 设置当前大小（取最新日期的数据）
      if (responseData.trendData && responseData.trendData.length > 0) {
        const latestData = responseData.trendData[responseData.trendData.length - 1];
        setCurrentSize(`${latestData.size} GB`);
        console.log('设置当前大小:', `${latestData.size} GB`);
      }
    } catch (error) {
      console.error('获取表趋势数据失败:', error);
      console.error('错误详情:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
      message.error('获取趋势数据失败');
      // 生成模拟数据作为备用
      const mockData = generateMockTrendData(range);
      setTrendData(mockData.trendData);
      setDailyIncrementData(mockData.dailyIncrementData);
      setCurrentSize('148.164 GB');
      console.log('使用模拟数据:', mockData);
    } finally {
      setLoading(false);
    }
  };

  // 弹窗打开时重置为默认状态并获取数据
  useEffect(() => {
    console.log('useEffect 触发:', { visible, databaseName, tableName });
    if (visible && databaseName && tableName) {
      console.log('弹窗打开，重置为14天并获取数据');
      // 重置时间范围为14天
      setTimeRange('14d');
      // 获取14天的数据
      fetchTrendData('14d');
    }
  }, [visible, databaseName, tableName]);

  // 如果必要参数缺失，不渲染弹窗
  if (!databaseName || !tableName) {
    console.warn('TableTrendModal: 缺少必要参数', { databaseName, tableName });
    return null;
  }

  return (
    <Modal
      title={`${databaseName}.${tableName} ${t('变化趋势分析')}`}
      visible={visible}
      onCancel={() => {
        console.log('用户点击关闭弹窗');
        onClose();
      }}
      footer={null}
      width={1400}
      destroyOnClose
      bodyStyle={{ padding: '16px', background: '#f5f5f5' }}
    >
      {/* 顶部统计信息 - 只显示当前大小和时间范围选择 */}
      <Card 
        size="small" 
        style={{ marginBottom: 12, boxShadow: '0 1px 2px rgba(0,0,0,0.05)' }}
        bodyStyle={{ padding: '8px', minHeight: 'auto' }}
      >
        <Row gutter={16} align="middle">
          <Col span={6}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 12, color: '#8c8c8c', marginBottom: 2 }}>{t('当前大小')}</div>
              <div style={{ fontSize: 16, fontWeight: 'bold', lineHeight: 1.2}}>
                {currentSize}
              </div>
            </div>
          </Col>
          <Col span={18}>
            <div style={{ textAlign: 'right' }}>
              <Radio.Group 
                value={timeRange} 
                onChange={e => handleTimeRangeChange(e.target.value)}
                size="middle"
              >
                <Radio.Button value="7d">{t('7天')}</Radio.Button>
                <Radio.Button value="14d">{t('14天')}</Radio.Button>
                <Radio.Button value="30d">{t('30天')}</Radio.Button>
              </Radio.Group>
            </div>
          </Col>
        </Row>
      </Card>
      
      {/* 趋势图表 */}
      <Card 
        title={t('大小变化趋势')} 
        size="small"
        style={{ marginBottom: 12 }}
        bodyStyle={{ height: 290 }}
        loading={loading}
      >
        <Line
          data={trendData}
          xField="date"
          yField="size"
          seriesField="database"
          xAxis={{
            // title: {
            //   text: t('日期'),
            // },
          }}
          yAxis={{
            title: {
              text: t('大小 (GB)'),
            },
          }}
          tooltip={{
            formatter: (datum) => {
              return { name: datum.database, value: `${datum.size} GB` };
            },
          }}
          annotations={[
            // 添加当前日期的垂直线
            {
              type: 'line',
              start: [new Date().toISOString().split('T')[0], 'min'],
              end: [new Date().toISOString().split('T')[0], 'max'],
              style: {
                stroke: '#1890ff',
                lineDash: [3, 3],
              },
            },
          ]}
        />
      </Card>
      
      <Card 
        title={t('每日增量')} 
        size="small"
        style={{ marginBottom: 12 }}
        bodyStyle={{ height: 290 }}
        loading={loading}
      >
        <Column
          data={dailyIncrementData}
          xField="date"
          yField="increment"
          seriesField="database"
          isStack={false}
          xAxis={{
            // title: {
            //   text: t('日期'),
            // },
          }}
          yAxis={{
            title: {
              text: t('增量 (GB)'),
            },
          }}
          tooltip={{
            formatter: (datum) => {
              return { name: datum.database, value: `${datum.increment > 0 ? '+' : ''}${datum.increment} GB` };
            },
          }}
          columnStyle={{
            stroke: '#000000',
            lineWidth: 1,
          }}
          color={({ increment }) => {
            return increment >= 0 ? '#f5222d' : '#52c41a';
          }}
        />
      </Card>
    </Modal>
  );
};

export default TableTrendModal;