import React, { useState, useEffect } from 'react';
import { Card, Input, Button, Spin, message, Descriptions } from 'antd';
import { SearchOutlined, LineChartOutlined } from '@ant-design/icons';
import { Bar } from '@ant-design/plots';
import { useTranslation } from 'react-i18next';
import { getDatabaseTableSizeDistribution } from '../../../../../services/diskAnalysisService';
import TableTrendModal from './TableTrendModal';
import { useTrendData } from '../hooks/useTrendData';
import { Database } from '../types';

interface DatabaseDetailProps {
  databaseInfo: Database;
  loading?: boolean;
}

interface TableData {
  tableName: string;
  rows: number;
  size: number;
  engine: string;
}

// 重命名为 TableSizeData 避免与 TableData 冲突
interface TableSizeData {
  tableName: string;
  rows: number;
  size: number;
  engine: string;
}

interface ApiResponse {
  success: boolean;
  message: string;
  data: TableSizeData[];
  total: number;
  tableCount: number;
}

const DatabaseDetail: React.FC<DatabaseDetailProps> = ({ 
  databaseInfo, 
  loading = false 
}) => {
  const { t } = useTranslation();
  const [searchText, setSearchText] = useState('');
  const [tableData, setTableData] = useState<TableSizeData[]>([]);
  const [tableLoading, setTableLoading] = useState(false);
  const [tableCount, setTableCount] = useState<number>(0);
  
  // 表趋势弹窗状态
  const [trendModalVisible, setTrendModalVisible] = useState(false);
  const [selectedTableName, setSelectedTableName] = useState<string>('');

  // 移除 useTrendData hook 的使用，因为我们现在使用独立的弹窗
  // 如果不需要原有的趋势功能，可以完全移除这部分

  // 获取表大小分布数据
  const fetchTableData = async () => {
    if (!databaseInfo?.name) return;
    
    setTableLoading(true);
    try {
      const response: ApiResponse = await getDatabaseTableSizeDistribution({
        databaseName: databaseInfo.name,
        sortBy: 'size',
        sortOrder: 'desc'
      });
      
      if (response.success && response.data) {
        setTableData(response.data);
        setTableCount(response.tableCount || response.total || 0);
      } else {
        message.error('获取表数据失败');
      }
    } catch (error) {
      console.error('获取表数据异常:', error);
      message.error('获取表数据异常');
    } finally {
      setTableLoading(false);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchTableData();
  }, [databaseInfo?.name]);

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  // 根据搜索文本过滤数据
  const filteredChartData = tableData.filter(item => 
    item.tableName.toLowerCase().includes(searchText.toLowerCase())
  );

  // 修改：处理表趋势点击 - 添加详细日志
  const handleTableTrendClick = (tableName: string) => {
    console.log('=== TableTrendModal Debug Info ===');
    console.log('点击的表名:', tableName);
    console.log('数据库名:', databaseInfo?.name);
    console.log('当前弹窗状态:', trendModalVisible);
    console.log('当前选中表名:', selectedTableName);
    
    if (!tableName) {
      console.error('错误: 表名为空');
      return;
    }
    
    if (!databaseInfo?.name) {
      console.error('错误: 数据库名为空');
      return;
    }
    
    setSelectedTableName(tableName);
    setTrendModalVisible(true);
    
    console.log('设置后的状态:');
    console.log('- selectedTableName 将设置为:', tableName);
    console.log('- trendModalVisible 将设置为: true');
    console.log('================================');
  };

  // 新增：关闭趋势弹窗 - 添加日志
  const handleCloseTrendModal = () => {
    console.log('关闭趋势弹窗');
    setTrendModalVisible(false);
    setSelectedTableName('');
  };

  // 自定义柱状图配置
  const config = {
    data: filteredChartData,
    xField: 'size',
    yField: 'tableName',
    seriesField: 'tableName',
    legend: false,
    meta: {
      size: {
        alias: t('大小 (GB)'),
      },
      tableName: {
        alias: t('表名'),
      },
    },
    label: {
      position: 'right' as const,
      formatter: (datum: any) => `${datum.size} GB`,
    },
    tooltip: {
      formatter: (datum: any) => {
        return {
          name: t('详细信息'),
          value: `${t('大小')}: ${datum.size} GB, ${t('行数')}: ${datum.rows ? datum.rows.toLocaleString() : 'N/A'}, ${t('存储引擎')}: ${datum.engine}`,
        };
      },
    },
    color: (datum: any) => {
      const maxSize = Math.max(...filteredChartData.map(d => d.size));
      const ratio = datum.size / maxSize;
      if (ratio > 0.8) return '#f5222d';
      if (ratio > 0.6) return '#fa8c16';
      if (ratio > 0.4) return '#faad14';
      if (ratio > 0.2) return '#52c41a';
      return '#1890ff';
    },
  };

  return (
    <div style={{ padding: '16px' }}>
      <Card size="small" style={{ marginBottom: '16px' }}>
        <Descriptions column={2} size="small">
          <Descriptions.Item label={t('数据库名称')}>
            {databaseInfo.name}
          </Descriptions.Item>
          <Descriptions.Item label={t('文件数')}>
            {databaseInfo.fileCount}
          </Descriptions.Item>
          <Descriptions.Item label={t('大小')}>
            {databaseInfo.currentSize}
          </Descriptions.Item>
          <Descriptions.Item label={t('表数量')}>
            {tableCount || databaseInfo.tableCount}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      <Card 
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>{t('表大小分布（从大到小）前100')}</span>
            <Input
              placeholder={t('搜索表名')}
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 200 }}
              allowClear
            />
          </div>
        }
        size="small"
        loading={tableLoading}
      >
        <div style={{ position: 'relative' }}>
          <Bar {...config} height={Math.max(200, filteredChartData.length * 40)} />
          {/* 变化趋势按钮覆盖层 */}
          <div style={{ position: 'absolute', top: 0, right: 0, pointerEvents: 'none' }}>
            {filteredChartData.map((item, index) => {
              const chartHeight = Math.max(200, filteredChartData.length * 40);
              const barHeight = chartHeight / filteredChartData.length;
              const topPosition = index * barHeight + barHeight / 2 - 12;
              
              return (
                <div 
                  key={item.tableName}
                  style={{
                    position: 'absolute',
                    top: `${topPosition}px`,
                    right: '10px',
                    pointerEvents: 'auto'
                  }}
                >
                  <Button 
                    size="small" 
                    type="default"
                    icon={<LineChartOutlined style={{ color: '#52c41a' }} />}
                    onClick={() => handleTableTrendClick(item.tableName)}
                    title={t('变化趋势')}
                    style={{
                      border: '1px solid #d9d9d9',
                      backgroundColor: '#f6ffed',
                      borderColor: '#b7eb8f'
                    }}
                  />
                </div>
              );
            })}
          </div>
        </div>
        
        {/* 搜索结果提示 */}
        {searchText && (
          <div style={{ marginTop: 8, color: '#666', fontSize: 12 }}>
            {t('找到')} {filteredChartData.length} {t('个匹配的表')}
            {filteredChartData.length === 0 && (
              <span style={{ color: '#999', marginLeft: 8 }}>
                {t('请尝试其他关键词')}
              </span>
            )}
          </div>
        )}
      </Card>

      {/* 趋势分析弹窗 - 添加调试信息 */}
      {console.log('渲染 TableTrendModal:', {
        visible: trendModalVisible,
        databaseName: databaseInfo.name,
        tableName: selectedTableName,
        hasTableTrendModal: !!TableTrendModal
      })}
      <TableTrendModal
        visible={trendModalVisible}
        onClose={handleCloseTrendModal}
        databaseName={databaseInfo.name}
        tableName={selectedTableName}
      />
    </div>
  );
};

export default DatabaseDetail;