import React, { useState, useEffect } from 'react';
import { Table, Space, Tooltip, Button, Card, Input, message, Empty } from 'antd';
import { LineChartOutlined, SearchOutlined, SyncOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { LargeFilesListProps, LargeFile } from '../types';
import { getLargeFilesList } from '../../../../../services/diskAnalysisService';

const LargeFilesList: React.FC<LargeFilesListProps> = ({
  loading: parentLoading,
  openTrendChart
}) => {
  const { t } = useTranslation();
  const [fileSearchText, setFileSearchText] = useState('');
  const [largeFiles, setLargeFiles] = useState<LargeFile[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取大文件列表数据
  const fetchLargeFiles = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await getLargeFilesList({
        page: 1,
        pageSize: 100, // 获取更多数据用于本地搜索和分页
        search: '', // 服务端搜索可以为空，使用本地搜索
      });
      
      if (response.success && response.data) {
        setLargeFiles(response.data.list || []);
      } else {
        throw new Error(response.message || '获取大文件列表失败');
      }
    } catch (err) {
      console.error('获取大文件列表失败:', err);
      const errorMessage = err instanceof Error ? err.message : '获取大文件列表失败';
      setError(errorMessage);
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchLargeFiles();
  }, []);

  // 过滤大文件列表
  const filteredFiles = largeFiles.filter(file => 
    file.fileName.toLowerCase().includes(fileSearchText.toLowerCase()) ||
    file.filePath.toLowerCase().includes(fileSearchText.toLowerCase())
  );

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: t('文件名'),
      dataIndex: 'fileName',
      key: 'fileName',
      width: 150,
      ellipsis: {
        showTitle: false,
      },
      render: (text) => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: t('文件路径'),
      dataIndex: 'filePath',
      key: 'filePath',
      width: 250,
      ellipsis: {
        showTitle: false,
      },
      render: (text) => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: t('文件大小'),
      dataIndex: 'fileSize',
      key: 'fileSize',
      width: 120,
      sorter: (a, b) => {
        // 解析文件大小进行排序（假设格式为 "15.2 GB"）
        const parseSize = (size: string) => {
          const match = size.match(/([\d.]+)\s*(\w+)/);
          if (!match) return 0;
          const value = parseFloat(match[1]);
          const unit = match[2].toUpperCase();
          const multipliers = { 'B': 1, 'KB': 1024, 'MB': 1024**2, 'GB': 1024**3, 'TB': 1024**4 };
          return value * (multipliers[unit] || 1);
        };
        return parseSize(a.fileSize) - parseSize(b.fileSize);
      },
      render: (text) => (
        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>
          {text}
        </span>
      ),
    },
    {
      title: t('近14日变化率'),
      dataIndex: 'changeRate14Days',
      key: 'changeRate14Days',
      width: 140,
      sorter: (a, b) => {
        // 解析变化率进行排序（去掉百分号）
        const parseRate = (rate?: string) => {
          if (!rate) return 0;
          return parseFloat(rate.replace('%', ''));
        };
        return parseRate(a.changeRate14Days) - parseRate(b.changeRate14Days);
      },
      render: (text, record) => {
        if (!text) {
          return <span style={{ color: '#8c8c8c' }}>-</span>;
        }
        
        const changeValue = record.changeValue14Days || 0;
        
        return (
          <div>
            <span style={{ color: changeValue > 0 ? '#f5222d' : '#52c41a' }}>
              {changeValue > 0 ? '+' : ''}{text}
            </span>
            <span style={{ marginLeft: 8, fontSize: '12px', color: '#8c8c8c' }}>
              ({changeValue > 0 ? '+' : ''}{changeValue.toFixed(2)} GB)
            </span>
          </div>
        );
      },
    },

    {
      title: t('操作'),
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<LineChartOutlined />}
            size="small"
            onClick={async () => {
              try {
                // 调用后端接口获取大文件的趋势数据
                const { getLargeFileTrendData } = await import('../../../../../services/diskAnalysisService');
                
                // 获取14天的趋势数据
                const trendResponse14d = await getLargeFileTrendData({
                  fileName: record.fileName,
                  filePath: record.filePath,
                  timeRange: '14d'
                });
                
                // 创建完整的记录对象，直接使用接口返回的数据
                const completeRecord = {
                  id: parseInt(record.key),
                  name: record.fileName,
                  instance: record.filePath,
                  currentSize: record.fileSize,
                  changeRate7Days: record.changeRate7Days || '0%',
                  changeValue7Days: record.changeValue7Days || 0,
                  changeRate14Days: record.changeRate14Days || '0%',
                  changeValue: record.changeValue14Days || 0, // 兼容旧字段名
                  changeRate30Days: record.changeRate30Days || '0%',
                  changeValue30Days: record.changeValue30Days || 0,
                  growthRate: 5.2,
                };
                
                // 调用父组件传递的openTrendChart函数
                openTrendChart(completeRecord, trendResponse14d.data.trendData, trendResponse14d.data.dailyIncrementData);
              } catch (error) {
                console.error('获取大文件趋势数据失败:', error);
                message.error('获取趋势数据失败，请稍后重试');
              }
            }}
          >
            {t('变化趋势')}
          </Button>
        </Space>
      ),
    },
  ];

  // 检查是否有搜索结果
  const hasSearchResults = filteredFiles.length > 0;
  const isSearching = fileSearchText.trim().length > 0;
  const hasData = largeFiles.length > 0;

  return (
    <Card 
      title={<span style={{ fontSize: '16px' }}>{t('大文件列表')}</span>}
      extra={(
        <Space>
          <Button 
            type="primary"
            size="small" 
            icon={<SyncOutlined />}
            onClick={fetchLargeFiles}
            loading={loading}
          >
            {t('刷新')}
          </Button>
          <Input
            placeholder={t('搜索文件名或路径')}
            prefix={<SearchOutlined />}
            style={{ width: 180 }}
            value={fileSearchText}
            onChange={e => setFileSearchText(e.target.value)}
            allowClear
            size="small"
          />
        </Space>
      )}
    >
      {error && (
        <div style={{ marginBottom: 16, padding: 8, background: '#fff2f0', border: '1px solid #ffccc7', borderRadius: 4 }}>
          <span style={{ color: '#ff4d4f' }}>错误: {error}</span>
          <Button type="link" size="small" onClick={fetchLargeFiles}>
            重试
          </Button>
        </div>
      )}
      
      <Table 
        columns={columns} 
        dataSource={filteredFiles} 
        pagination={{ 
          pageSize: 8,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} / ${total} ${t('条记录')}`,
        }}
        loading={loading || parentLoading}
        scroll={{ x: 1200 }}
        locale={{
          emptyText: (
            <Empty 
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                error ? (
                  <span style={{ color: '#8c8c8c' }}>
                    {t('数据加载失败')}<br/>
                    <Button type="link" size="small" onClick={fetchLargeFiles}>
                      {t('点击重试')}
                    </Button>
                  </span>
                ) : isSearching ? (
                  <span style={{ color: '#8c8c8c' }}>
                    {t('未找到匹配的文件')}<br/>
                    <span style={{ fontSize: '12px' }}>
                      {t('尝试修改搜索条件或')}
                      <Button 
                        type="link" 
                        size="small" 
                        style={{ padding: 0, fontSize: '12px' }}
                        onClick={() => setFileSearchText('')}
                      >
                        {t('清空搜索')}
                      </Button>
                    </span>
                  </span>
                ) : !hasData ? (
                  <span style={{ color: '#8c8c8c' }}>
                    {t('暂无大文件数据')}<br/>
                    <span style={{ fontSize: '12px' }}>{t('请检查数据源连接或稍后重试')}</span>
                  </span>
                ) : (
                  <span style={{ color: '#8c8c8c' }}>
                    {t('暂无数据')}
                  </span>
                )
              }
            />
          )
        }}
      />
    </Card>
  );
};

export default LargeFilesList;