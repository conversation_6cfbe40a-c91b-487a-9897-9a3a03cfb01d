import React from 'react';
import { Mo<PERSON>, Card, Row, Col, Radio, Statistic, Divider } from 'antd';
import { Line, Column } from '@ant-design/plots';
import { useTranslation } from 'react-i18next';
import { TrendModalProps, TimeRangeType } from '../types';

const TrendModal: React.FC<TrendModalProps> = ({
  visible,
  onCancel,
  selectedDatabase,
  trendData,
  dailyIncrementData,
  timeRange,
  handleTimeRangeChange
}) => {
  const { t } = useTranslation();

  // 解析changeRate字段，去掉百分号并转换为数字
  const parseChangeRate = (changeRate?: string) => {
    if (!changeRate) return '0.0';
    return changeRate.replace('%', '');
  };

  return (
    <Modal
      title={`${selectedDatabase?.name} ${t('变化趋势分析')}`}
      visible={visible}
      onCancel={onCancel}
      footer={null}
      width={1400}
      destroyOnClose
      bodyStyle={{ padding: '16px', background: '#f5f5f5' }}
    >
      {/* 顶部统计信息 - 更加紧凑 */}
      <Card 
        size="small" 
        style={{ marginBottom: 12, boxShadow: '0 1px 2px rgba(0,0,0,0.05)' }}
        bodyStyle={{ padding: '8px', minHeight: 'auto' }}
      >
        <Row gutter={16} align="middle">
          <Col span={4}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 12, color: '#8c8c8c', marginBottom: 2 }}>{t('当前大小')}</div>
              <div style={{ fontSize: 16, fontWeight: 'bold', lineHeight: 1.2}}>
                {selectedDatabase?.currentSize} <span style={{ fontSize: 12 }}></span>
              </div>
            </div>
          </Col>
          <Col span={4}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 12, color: '#8c8c8c', marginBottom: 2 }}>{t('7日增长率')}</div>
              <div style={{ fontSize: 16, fontWeight: 'bold', lineHeight: 1.2, color: '#f5222d' }}>
                <div>
                  {selectedDatabase?.changeValue7Days && selectedDatabase.changeValue7Days > 0 ? '+' : ''}{parseChangeRate(selectedDatabase?.changeRate7Days)}%
                </div>
                <div style={{ fontSize: 12, color: '#8c8c8c', fontWeight: 'normal', marginTop: 2 }}>
                  ({selectedDatabase?.changeValue7Days && selectedDatabase.changeValue7Days > 0 ? '+' : ''}{typeof selectedDatabase?.changeValue7Days === 'number' ? selectedDatabase.changeValue7Days.toFixed(2) : selectedDatabase?.changeValue7Days || '0.00'} GB)
                </div>
              </div>
            </div>
          </Col>
          <Col span={4}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 12, color: '#8c8c8c', marginBottom: 2 }}>{t('14日增长率')}</div>
              <div style={{ fontSize: 16, fontWeight: 'bold', lineHeight: 1.2, color: '#f5222d' }}>
                <div>
                  {selectedDatabase?.changeValue && selectedDatabase.changeValue > 0 ? '+' : ''}{parseChangeRate(selectedDatabase?.changeRate14Days)}%
                </div>
                <div style={{ fontSize: 12, color: '#8c8c8c', fontWeight: 'normal', marginTop: 2 }}>
                  ({selectedDatabase?.changeValue && selectedDatabase.changeValue > 0 ? '+' : ''}{typeof selectedDatabase?.changeValue === 'number' ? selectedDatabase.changeValue.toFixed(2) : selectedDatabase?.changeValue || '0.00'} GB)
                </div>
              </div>
            </div>
          </Col>
          <Col span={4}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 12, color: '#8c8c8c', marginBottom: 2 }}>{t('30日增长率')}</div>
              <div style={{ fontSize: 16, fontWeight: 'bold', lineHeight: 1.2, color: '#f5222d' }}>
                <div>
                  {selectedDatabase?.changeValue30Days && selectedDatabase.changeValue30Days > 0 ? '+' : ''}{parseChangeRate(selectedDatabase?.changeRate30Days)}%
                </div>
                <div style={{ fontSize: 12, color: '#8c8c8c', fontWeight: 'normal', marginTop: 2 }}>
                  ({selectedDatabase?.changeValue30Days && selectedDatabase.changeValue30Days > 0 ? '+' : ''}{typeof selectedDatabase?.changeValue30Days === 'number' ? selectedDatabase.changeValue30Days.toFixed(2) : selectedDatabase?.changeValue30Days || '0.00'} GB)
                </div>
              </div>
            </div>
          </Col>
          <Col span={8}>
            <div style={{ textAlign: 'right' }}>
              <Radio.Group 
                value={timeRange} 
                onChange={e => handleTimeRangeChange(e.target.value)}
                size="middle"
              >
                <Radio.Button value="7d">{t('7天')}</Radio.Button>
                <Radio.Button value="14d">{t('14天')}</Radio.Button>
                <Radio.Button value="30d">{t('30天')}</Radio.Button>
              </Radio.Group>
            </div>
          </Col>
        </Row>
      </Card>
      
      {/* 趋势图表 */}
      {/* 移foreign from Row and Col, let two Card directly below each other */}
      <Card 
        title={t('大小变化趋势')} 
        size="small"
        style={{ marginBottom: 12 }} // 保持底部间距
        bodyStyle={{ height: 290 }}
      >
        <Line
          data={trendData}
          xField="date"
          yField="size"
          seriesField="database"
          xAxis={{
            // title: {
            //   text: t('日期'),
            // },
          }}
          yAxis={{
            title: {
              text: t('大小 (GB)'),
            },
          }}
          tooltip={{
            formatter: (datum) => {
              return { name: datum.database, value: `${datum.size} GB` };
            },
          }}
          annotations={[
            // 添加当前日期的垂直线
            {
              type: 'line',
              start: [new Date().toISOString().split('T')[0], 'min'],
              end: [new Date().toISOString().split('T')[0], 'max'],
              style: {
                stroke: '#1890ff',
                lineDash: [3, 3],
              },
            },
          ]}
        />
      </Card>
      
      <Card 
        title={t('每日增量')} 
        size="small"
        style={{ marginBottom: 12 }} // 保持底部间距
        bodyStyle={{ height: 290 }}
      >
        <Column
          data={dailyIncrementData}
          xField="date"
          yField="increment"
          seriesField="database"
          isStack={false}
          xAxis={{
            // title: {
            //   text: t('日期'),
            // },
          }}
          yAxis={{
            title: {
              text: t('增量 (GB)'),
            },
          }}
          tooltip={{
            formatter: (datum) => {
              return { name: datum.database, value: `${datum.increment > 0 ? '+' : ''}${datum.increment} GB` };
            },
          }}
          columnStyle={{
            stroke: '#000000',
            lineWidth: 1,
          }}
          color={({ increment }) => {
            return increment >= 0 ? '#f5222d' : '#52c41a';
          }}
        />
      </Card>
      
      {/* 预测和建议 */}
      {/* 移除增长预测与建议功能 */}
    </Modal>
  );
};

export default TrendModal;