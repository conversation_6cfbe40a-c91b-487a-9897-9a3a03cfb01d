// 导出所有hooks，提供统一的导入入口

export { default as usePrometheusData } from './usePrometheusData';
export { default as useAIAnalysis } from './useAIAnalysis';
export { default as useTrendData } from './useTrendData';

// 也可以导出hooks的类型（如果有特定类型需要导出）
export type { PrometheusDataHookResult } from './usePrometheusData';
export type { AIAnalysisHookResult } from './useAIAnalysis';
export type { TrendDataHookResult } from './useTrendData';