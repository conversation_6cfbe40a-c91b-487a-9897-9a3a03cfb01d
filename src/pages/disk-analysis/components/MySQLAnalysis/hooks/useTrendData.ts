import { useState } from 'react';
import { Database, TrendData, DailyIncrementData, TimeRangeType } from '../types';
import { TREND_PATTERNS } from '../constants';

// 定义Hook返回值类型
export interface TrendDataHookResult {
  trendData: TrendData[];
  dailyIncrementData: DailyIncrementData[];
  selectedDatabase: Database | null;
  trendModalVisible: boolean;
  timeRange: TimeRangeType;
  setTrendModalVisible: (visible: boolean) => void;
  setSelectedDatabase: (database: Database | null) => void;
  openTrendChart: (database: Database) => void;
  handleTimeRangeChange: (range: TimeRangeType) => void;
  generateTrendData: (database: Database, range?: TimeRangeType) => TrendData[];
  generateDailyIncrementData: (database: Database, range?: TimeRangeType) => DailyIncrementData[];
}

/**
 * 自定义Hook，用于生成趋势数据和每日增量数据
 * @returns 趋势数据状态和处理函数
 */
const useTrendData = (): TrendDataHookResult => {
  // 状态定义
  const [trendData, setTrendData] = useState<TrendData[]>([]);
  const [dailyIncrementData, setDailyIncrementData] = useState<DailyIncrementData[]>([]);
  const [selectedDatabase, setSelectedDatabase] = useState<Database | null>(null);
  const [trendModalVisible, setTrendModalVisible] = useState(false);
  const [timeRange, setTimeRange] = useState<TimeRangeType>('14d');

  /**
   * 生成趋势数据
   * @param database 数据库信息
   * @param range 时间范围
   * @returns 趋势数据数组
   */
  const generateTrendData = (database: Database, range: TimeRangeType = '14d'): TrendData[] => {
    const now = new Date();
    const data: TrendData[] = [];
    
    // 根据选择的时间范围确定天数
    const pastDays = range === '7d' ? 7 : range === '14d' ? 14 : 30;
    
    // 解析当前大小字符串，提取数值和单位
    let baseSize = 0;
    if (typeof database.currentSize === 'string') {
      const match = database.currentSize.match(/([\.\d]+)\s*(\w+)/);
      if (match) {
        const value = parseFloat(match[1]);
        baseSize = value * 0.9; // 从当前大小的90%开始
      } else {
        baseSize = 10; // 默认值
      }
    } else {
      baseSize = database.currentSize * 0.9; // 兼容数字类型
    }
    
    // 基准大小和波动模式
    const patterns = TREND_PATTERNS;
    
    // 生成过去的数据，添加明显的波动
    let currentSize = baseSize;
    for (let i = pastDays; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      
      // 使用波动模式生成数据
      const patternIndex = (pastDays - i) % patterns.length;
      const changeFactor = patterns[patternIndex];
      
      // 应用变化因子
      currentSize = currentSize * changeFactor;
      
      // 添加小的随机波动
      const randomFactor = 0.995 + Math.random() * 0.01; // 0.995到1.005之间的随机因子
      currentSize = currentSize * randomFactor;
      
      // 保存数据时保持精确值，仅在显示时四舍五入
      data.push({
        date: dateStr,
        size: Math.round(currentSize * 100) / 100, // 保留两位小数，使用Math.round确保精确计算
        database: database.name
      });
    }
    
    return data;
  };
  
  /**
   * 生成每日增量数据
   * @param database 数据库信息
   * @param range 时间范围
   * @returns 每日增量数据数组
   */
  const generateDailyIncrementData = (database: Database, range: TimeRangeType = '14d'): DailyIncrementData[] => {
    // 首先获取总量数据
    const totalData = generateTrendData(database, range);
    const data: DailyIncrementData[] = [];
    
    // 计算每日增量（当前日期与前一天的差值）
    for (let i = 1; i < totalData.length; i++) {
      const currentDay = totalData[i];
      const previousDay = totalData[i-1];
      
      // 直接计算差值，确保与总量数据完全一致
      const increment = Number((currentDay.size - previousDay.size).toFixed(2));
      
      data.push({
        date: currentDay.date,
        increment: increment, // 使用精确的差值，不进行额外处理
        database: database.name
      });
    }
    
    return data;
  };

  /**
   * 打开趋势图表
   * @param database 数据库信息
   * @param initialTrendData 初始趋势数据（可选）
   * @param initialDailyData 初始每日增量数据（可选）
   */
  const openTrendChart = async (
    database: Database, 
    initialTrendData?: TrendData[], 
    initialDailyData?: DailyIncrementData[]
  ) => {
    setSelectedDatabase(database);
    // 每次打开趋势图时重置时间范围为14天
    setTimeRange('14d');
    
    if (initialTrendData && initialDailyData) {
      // 如果传入了初始数据，直接使用
      setTrendData(initialTrendData);
      setDailyIncrementData(initialDailyData);
    } else {
      // 否则请求14天的数据
      try {
        // 根据数据类型判断使用哪个接口
        if (database.name === '所有数据库') {
          // 数据库概览趋势
          const { getDatabaseOverviewTrendData } = await import('../../../../../services/diskAnalysisService');
          const trendResponse = await getDatabaseOverviewTrendData({
            timeRange: '14d'
          });
          setTrendData(trendResponse.data.trendData);
          setDailyIncrementData(trendResponse.data.dailyIncrementData);
        } else if (database.instance && database.instance.includes('/')) {
          // 大文件趋势（通过路径判断）
          const { getLargeFileTrendData } = await import('../../../../../services/diskAnalysisService');
          const trendResponse = await getLargeFileTrendData({
            fileName: database.name,
            filePath: database.instance,
            timeRange: '14d'
          });
          setTrendData(trendResponse.data.trendData);
          setDailyIncrementData(trendResponse.data.dailyIncrementData);
        } else {
          // 数据库趋势
          const { getDatabaseTrendData } = await import('../../../../../services/diskAnalysisService');
          const trendResponse = await getDatabaseTrendData({
            databaseName: database.name,
            timeRange: '14d'
          });
          setTrendData(trendResponse.data.trendData);
          setDailyIncrementData(trendResponse.data.dailyIncrementData);
        }
      } catch (error) {
        console.error('获取趋势数据失败:', error);
        // 如果接口失败，使用生成的数据作为备用
        setTrendData(generateTrendData(database, '14d'));
        setDailyIncrementData(generateDailyIncrementData(database, '14d'));
      }
    }
    
    setTrendModalVisible(true);
  };

  /**
   * 处理时间范围变化
   * @param range 新的时间范围
   */
  const handleTimeRangeChange = async (range: TimeRangeType) => {
    setTimeRange(range);
    
    if (selectedDatabase) {
      try {
        // 根据数据类型判断使用哪个接口
        if (selectedDatabase.name === '所有数据库') {
          // 数据库概览趋势
          const { getDatabaseOverviewTrendData } = await import('../../../../../services/diskAnalysisService');
          const trendResponse = await getDatabaseOverviewTrendData({
            timeRange: range
          });
          setTrendData(trendResponse.data.trendData);
          setDailyIncrementData(trendResponse.data.dailyIncrementData);
        } else if (selectedDatabase.instance && selectedDatabase.instance.includes('/')) {
          // 大文件趋势（通过路径判断）
          const { getLargeFileTrendData } = await import('../../../../../services/diskAnalysisService');
          const trendResponse = await getLargeFileTrendData({
            fileName: selectedDatabase.name,
            filePath: selectedDatabase.instance,
            timeRange: range
          });
          setTrendData(trendResponse.data.trendData);
          setDailyIncrementData(trendResponse.data.dailyIncrementData);
        } else if (selectedDatabase.name.includes('.')) {
          // 表级别趋势（通过名称中包含点号判断）
          const [databaseName, tableName] = selectedDatabase.name.split('.');
          const { getDatabaseTableTrendData } = await import('../../../../../services/diskAnalysisService');
          const trendResponse = await getDatabaseTableTrendData({
            databaseName: databaseName,
            tableName: tableName,
            timeRange: range
          });
          setTrendData(trendResponse.data.trendData);
          setDailyIncrementData(trendResponse.data.dailyIncrementData);
        } else {
          // 数据库趋势
          const { getDatabaseTrendData } = await import('../../../../../services/diskAnalysisService');
          const trendResponse = await getDatabaseTrendData({
            databaseName: selectedDatabase.name,
            timeRange: range
          });
          setTrendData(trendResponse.data.trendData);
          setDailyIncrementData(trendResponse.data.dailyIncrementData);
        }
      } catch (error) {
        console.error('获取趋势数据失败:', error);
        // 如果接口失败，使用生成的数据作为备用
        setTrendData(generateTrendData(selectedDatabase, range));
        setDailyIncrementData(generateDailyIncrementData(selectedDatabase, range));
      }
    }
  };

  return {
    trendData,
    dailyIncrementData,
    selectedDatabase,
    trendModalVisible,
    timeRange,
    setTrendModalVisible,
    setSelectedDatabase,
    openTrendChart,
    handleTimeRangeChange,
    generateTrendData,
    generateDailyIncrementData
  };
};

export default useTrendData;