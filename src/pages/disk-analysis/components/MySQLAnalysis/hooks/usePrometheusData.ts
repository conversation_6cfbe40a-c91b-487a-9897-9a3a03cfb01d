import { useState, useEffect, useCallback } from 'react';
import { Database, PrometheusData } from '../types';
import { getDatabaseList } from '../../../../../services/diskAnalysisService'; // 删除 getDatabaseOverview 导入

// 定义Hook返回值类型
export interface PrometheusDataHookResult {
  loading: boolean;
  databases: Database[];
  prometheusData: PrometheusData | null;
  filteredDatabases: Database[]; // 修改为直接返回数组
  refreshData: () => Promise<void>;
  error?: string; // 新增错误状态
}

/**
 * 自定义Hook，用于获取和处理MySQL分析数据
 * @param initialSearchText 初始搜索文本
 * @returns 数据状态、加载状态和刷新函数
 */
const usePrometheusData = (initialSearchText: string = ''): PrometheusDataHookResult => {
  // 状态定义
  const [loading, setLoading] = useState(false);
  const [databases, setDatabases] = useState<Database[]>([]);
  const [prometheusData, setPrometheusData] = useState<PrometheusData | null>(null);
  const [filteredDatabases, setFilteredDatabases] = useState<Database[]>([]); // 新增状态变量
  const [error, setError] = useState<string>(); // 新增错误状态

  // 过滤数据库列表的内部函数
  const filterDatabases = useCallback((dbs: Database[], searchText: string): Database[] => {
    return dbs.filter(item => 
      item.name.toLowerCase().includes(searchText.toLowerCase()) || 
      item.instance.toLowerCase().includes(searchText.toLowerCase())
    );
  }, []);

  // 从API获取数据
  const fetchPrometheusData = async (): Promise<void> => {
    setLoading(true);
    setError(undefined);
    
    try {
      // 只获取数据库列表，删除概览数据的请求
      const databaseListResponse = await getDatabaseList({
        page: 1,
        pageSize: 100, // 获取所有数据
        sortBy: 'currentSize',
        sortOrder: 'desc'
      });

      // 删除概览数据处理逻辑
      // if (overviewResponse.success && overviewResponse.data) {
      //   console.log('数据库概览数据:', overviewResponse.data);
      // }

      // 处理数据库列表数据
      if (databaseListResponse.success && databaseListResponse.data) {
        const { list } = databaseListResponse.data;
        
        // 转换API数据为组件所需的Database格式
        const transformedDatabases: Database[] = list.map((item: any) => ({
          id: item.id || item.name,
          name: item.name,
          instance: item.instance || 'localhost:3306',
          currentSize: item.currentSize || 0,
          tableCount: item.tableCount || 0,
          fileCount: item.fileCount || 0,
          changeRate14Days: item.changeRate14Days || '0%',
          changeValue: item.changeValue || 0,
          lastUpdated: item.lastUpdated || new Date().toISOString(),
          status: item.status || 'active'
        }));

        setDatabases(transformedDatabases);
        setFilteredDatabases(filterDatabases(transformedDatabases, initialSearchText));
        
        // 构造PrometheusData格式（如果需要的话）
        const prometheusData: PrometheusData = {
          mysql_database_size: transformedDatabases.map(db => ({
            database: db.name,
            instance: db.instance,
            value: db.currentSize,
            timestamp: Date.now()
          })),
          mysql_table_count: transformedDatabases.map(db => ({
            database: db.name,
            instance: db.instance,
            value: db.tableCount,
            timestamp: Date.now()
          }))
        };
        
        setPrometheusData(prometheusData);
      } else {
        // API返回失败，设置错误状态
        const errorMsg = databaseListResponse.message || '获取数据失败';
        setError(errorMsg);
        console.error('API返回失败:', errorMsg);
      }
      
    } catch (error: any) {
      console.error('获取MySQL分析数据失败:', error);
      setError(error.message || '网络请求失败，请检查网络连接或稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 初始加载数据和搜索文本变化时更新过滤数据
  useEffect(() => {
    fetchPrometheusData();
  }, []);

  useEffect(() => {
    setFilteredDatabases(filterDatabases(databases, initialSearchText));
  }, [databases, initialSearchText, filterDatabases]);

  return {
    loading,
    databases,
    prometheusData,
    filteredDatabases, // 直接返回数组
    refreshData: fetchPrometheusData,
    error
  };
};

export default usePrometheusData;