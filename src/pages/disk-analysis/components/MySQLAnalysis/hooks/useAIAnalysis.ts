import { useState, useCallback } from 'react';
import { Database, ChatMessage, PrometheusData } from '../types';
import { INITIAL_CHAT_MESSAGE, MOCK_AI_ANALYSIS_RESULT } from '../constants';

// 定义Hook返回值类型
export interface AIAnalysisHookResult {
  aiLoading: boolean;
  aiAnalysisResult: string;
  chatMessages: ChatMessage[];
  userQuestion: string;
  setUserQuestion: (question: string) => void;
  analyzeWithDify: () => Promise<void>;
  sendQuestion: () => Promise<void>;
}

/**
 * 处理MySQL数据库AI分析和聊天功能的自定义Hook
 * @param filteredDatabases 过滤后的数据库列表
 * @param prometheusData Prometheus数据
 * @returns AI分析状态和操作函数
 */
const useAIAnalysis = (
  filteredDatabases: Database[],
  prometheusData: PrometheusData
): AIAnalysisHookResult => {
  // AI分析状态
  const [aiLoading, setAiLoading] = useState<boolean>(false);
  const [aiAnalysisResult, setAiAnalysisResult] = useState<string>('');
  
  // 聊天状态
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([INITIAL_CHAT_MESSAGE]);
  const [userQuestion, setUserQuestion] = useState<string>('');
  
  /**
   * 使用Dify API分析数据
   */
  const analyzeWithDify = useCallback(async () => {
    setAiLoading(true);
    try {
      // 这里应该是实际调用Dify API的代码
      // 准备发送给Dify的数据
      const dataForAnalysis = {
        databases: filteredDatabases,
        prometheusMetrics: prometheusData,
        timeRange: '最近30天'
      };
      
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 获取用户数据库大小
      const userDbSize = filteredDatabases.find(db => db.name === 'user_db')?.currentSize || 0;
      const orderDbSize = filteredDatabases.find(db => db.name === 'order_db')?.currentSize || 0;
      const productDbSize = filteredDatabases.find(db => db.name === 'product_db')?.currentSize || 0;
      
      // 使用模拟分析结果
      const mockResult = MOCK_AI_ANALYSIS_RESULT(
        filteredDatabases.length,
        userDbSize,
        orderDbSize,
        productDbSize
      );
      
      setAiAnalysisResult(mockResult);
    } catch (error) {
      console.error('调用Dify API分析失败:', error);
    } finally {
      setAiLoading(false);
    }
  }, [filteredDatabases, prometheusData]);
  
  /**
   * 发送用户问题到AI助手
   */
  const sendQuestion = useCallback(async () => {
    if (!userQuestion.trim()) return;
    
    // 添加用户消息到聊天
    const updatedMessages = [...chatMessages, {role: 'user', content: userQuestion}];
    setChatMessages(updatedMessages);
    
    setAiLoading(true);
    try {
      // 这里应该是实际调用Dify API的代码
      // 准备发送给Dify的数据
      const questionData = {
        question: userQuestion,
        context: {
          databases: filteredDatabases,
          prometheusMetrics: prometheusData
        }
      };
      
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // 模拟Dify返回的回答
      const totalSize = filteredDatabases.reduce((sum, db) => sum + db.currentSize, 0);
      const aiResponse = `感谢您的问题。根据当前数据分析，我们有${filteredDatabases.length}个数据库，总容量为${totalSize}GB。您可以查看详细的分析报告获取更多信息。`;
      
      // 添加AI回复到聊天
      setChatMessages([...updatedMessages, {role: 'ai', content: aiResponse}]);
      setUserQuestion('');
    } catch (error) {
      console.error('调用Dify API对话失败:', error);
      // 添加错误消息
      setChatMessages([...updatedMessages, {role: 'ai', content: '抱歉，处理您的问题时出现了错误。请稍后再试。'}]);
      setUserQuestion('');
    } finally {
      setAiLoading(false);
    }
  }, [userQuestion, chatMessages, filteredDatabases, prometheusData]);

  return {
    aiLoading,
    aiAnalysisResult,
    chatMessages,
    userQuestion,
    setUserQuestion,
    analyzeWithDify,
    sendQuestion
  };
};

export default useAIAnalysis;