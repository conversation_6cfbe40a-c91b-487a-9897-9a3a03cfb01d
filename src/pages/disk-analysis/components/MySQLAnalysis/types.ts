// 数据库信息接口
export interface Database {
  id: number;
  name: string;
  instance: string;
  currentSize: string; // 修改为字符串类型
  growthRate?: number;
  changeRate14Days?: string;
  changeValue?: number;
  changeRate7Days?: string;   // 新增7日变化率
  changeValue7Days?: number;  // 新增7日变化值
  changeRate30Days?: string;  // 新增30日变化率
  changeValue30Days?: number; // 新增30日变化值
  fileCount?: number;
  tableCount?: number;
  lastUpdated?: string;
  status?: string;
}

// 趋势数据接口
export interface TrendData {
  date: string;
  size: number;
  database: string;
}

// 每日增量数据接口
export interface DailyIncrementData {
  date: string;
  increment: number;
  database: string;
}

// 大文件信息接口
export interface LargeFile {
  key: string;
  fileName: string;
  filePath: string;
  fileSize: string;
  changeRate14Days?: string;  // 新增：近14日变化率
  changeValue14Days?: number; // 新增：近14日变化值
  databaseName?: string;
  lastModified?: string;
}

// 聊天消息接口
export interface ChatMessage {
  role: 'user' | 'ai';
  content: string;
}

// 数据库概览数据接口
export interface DatabaseOverviewData {
  key: string;
  totalCount: number;
  totalTables: number;
  totalSize: string;
  changeRate7Days: string;   // 新增近7日变化率字段
  changeValue7Days: number;  // 新增近7日变化值字段
  changeRate14Days: string;
  changeValue: number;
  changeRate30Days: string;  // 新增近30日变化率字段
  changeValue30Days: number; // 新增近30日变化值字段
  version: string;  // 新增版本号字段
  path: string;     // 新增路径字段
  binlogEnabled?: boolean;   // 新增binlog启用状态
  binlogTotalSize?: number;  // 新增binlog总大小(GB)
}

// 时间范围类型
export type TimeRangeType = '7d' | '14d' | '30d';

// 组件Props接口
export interface MySQLAnalysisProps {
  searchText: string;
}

export interface DatabaseOverviewProps {
  filteredDatabases: Database[];
  loading: boolean;
  openTrendChart: (database: Database) => void;
}

export interface DatabaseListProps {
  openTrendChart: (database: Database) => void;
  filteredDatabases: Database[];
  loading: boolean;
  localSearchText: string;
  setLocalSearchText: (text: string) => void;
  // openTrendChart: (database: Database) => void;
  // onAddDetailTab: (databaseName: string) => void; // 可以移除这行
}

export interface LargeFilesListProps {
  loading: boolean;
  openTrendChart: (database: Database) => void;
}

export interface AIAnalysisTabProps {
  aiLoading: boolean;
  aiAnalysisResult: string;
  analyzeWithDify: () => void;
  trendData: TrendData[];
  chatMessages: ChatMessage[];
  userQuestion: string;
  setUserQuestion: (question: string) => void;
  sendQuestion: () => void;
}

export interface TrendModalProps {
  visible: boolean;
  onCancel: () => void;
  selectedDatabase: Database | null;
  trendData: TrendData[];
  dailyIncrementData: DailyIncrementData[];
  timeRange: TimeRangeType;
  handleTimeRangeChange: (range: TimeRangeType) => void;
}

// Prometheus数据接口
export interface PrometheusData {
  mysql_database_size: {
    database: string;
    instance: string;
    value: number;
    timestamp: number;
  }[];
}


export interface DatabaseDetailData {
  name: string;
  size: string;
  createdAt: string;
  tableCount: number;
  tables: TableInfo[];
}

export interface TableInfo {
  tableName: string;
  size: string;
  rowCount: number;
}


// 数据库概览趋势响应数据接口（简化版）
export interface DatabaseOverviewTrendResponse {
  // 趋势数据（用于大小变化趋势图）
  trendData: TrendData[];
  // 每日增量数据（用于每日增量图）
  dailyIncrementData: DailyIncrementData[];
}