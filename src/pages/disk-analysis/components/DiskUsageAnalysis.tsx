import React from 'react';
import { Table, Tag, Space, Progress, Tooltip, Button } from 'antd';
import { InfoCircleOutlined, WarningOutlined, LineChartOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { mockDisks } from '../mockData';

interface DiskUsageAnalysisProps {
  searchText: string;
}

const DiskUsageAnalysis: React.FC<DiskUsageAnalysisProps> = ({ searchText }) => {
  const { t } = useTranslation();
  
  // 过滤数据
  const filteredDisks = mockDisks.filter(item => 
    item.path.toLowerCase().includes(searchText.toLowerCase()) || 
    item.mountPoint.toLowerCase().includes(searchText.toLowerCase())
  );
  
  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: t('设备路径'),
      dataIndex: 'path',
      key: 'path',
    },
    {
      title: t('挂载点'),
      dataIndex: 'mountPoint',
      key: 'mountPoint',
    },
    {
      title: t('总容量'),
      dataIndex: 'totalSize',
      key: 'totalSize',
      render: (size) => `${size} GB`,
    },
    {
      title: t('已用容量'),
      dataIndex: 'usedSize',
      key: 'usedSize',
      render: (size) => `${size} GB`,
    },
    {
      title: t('使用率'),
      dataIndex: 'usagePercent',
      key: 'usagePercent',
      render: (percent) => (
        <Tooltip title={`${percent}% 已使用`}>
          <Progress 
            percent={percent} 
            size="small" 
            status={percent > 80 ? 'exception' : 'normal'}
          />
        </Tooltip>
      ),
    },
    {
      title: t('增长率'),
      dataIndex: 'growthRate',
      key: 'growthRate',
      render: (rate) => (
        <span style={{ color: rate > 10 ? '#f5222d' : '#52c41a' }}>
          {rate}% / 月
        </span>
      ),
    },
    {
      title: t('预计耗尽时间'),
      dataIndex: 'estimatedFullDate',
      key: 'estimatedFullDate',
      render: (date, record) => (
        <span>
          {date}
          {record.usagePercent > 80 && (
            <Tooltip title={t('容量即将耗尽')}>
              <WarningOutlined style={{ color: '#faad14', marginLeft: 8 }} />
            </Tooltip>
          )}
        </span>
      ),
    },
    {
      title: t('操作'),
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" icon={<LineChartOutlined />}>
            {t('趋势分析')}
          </Button>
          <Button type="link" icon={<InfoCircleOutlined />}>
            {t('详情')}
          </Button>
        </Space>
      ),
    },
  ];
  
  return (
    <Table 
      columns={columns} 
      dataSource={filteredDisks} 
      rowKey="id"
      pagination={{ pageSize: 10 }}
    />
  );
};

export default DiskUsageAnalysis;