import React from 'react';
import { Table, Tag, Space, Progress, Tooltip, Button } from 'antd';
import { InfoCircleOutlined, LineChartOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { mockContainers } from '../mockData';

interface ContainerAnalysisProps {
  searchText: string;
}

const ContainerAnalysis: React.FC<ContainerAnalysisProps> = ({ searchText }) => {
  const { t } = useTranslation();
  
  // 过滤数据
  const filteredContainers = mockContainers.filter(item => 
    item.name.toLowerCase().includes(searchText.toLowerCase()) || 
    item.image.toLowerCase().includes(searchText.toLowerCase())
  );
  
  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: t('容器名称'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('镜像'),
      dataIndex: 'image',
      key: 'image',
    },
    {
      title: t('状态'),
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        let color = 'green';
        if (status === 'stopped') {
          color = 'volcano';
        } else if (status === 'paused') {
          color = 'geekblue';
        }
        return (
          <Tag color={color}>
            {status.toUpperCase()}
          </Tag>
        );
      },
    },
    {
      title: t('磁盘使用'),
      dataIndex: 'diskUsage',
      key: 'diskUsage',
      render: (usage) => `${usage} GB`,
    },
    {
      title: t('增长趋势'),
      dataIndex: 'growthTrend',
      key: 'growthTrend',
      render: (trend) => {
        let color = '#52c41a';
        let text = t('稳定');
        
        if (trend === 'rapid') {
          color = '#f5222d';
          text = t('快速增长');
        } else if (trend === 'moderate') {
          color = '#faad14';
          text = t('中度增长');
        }
        
        return <span style={{ color }}>{text}</span>;
      },
    },
    {
      title: t('操作'),
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" icon={<LineChartOutlined />}>
            {t('趋势分析')}
          </Button>
          <Button type="link" icon={<InfoCircleOutlined />}>
            {t('详情')}
          </Button>
        </Space>
      ),
    },
  ];
  
  return (
    <Table 
      columns={columns} 
      dataSource={filteredContainers} 
      rowKey="id"
      pagination={{ pageSize: 10 }}
    />
  );
};

export default ContainerAnalysis;