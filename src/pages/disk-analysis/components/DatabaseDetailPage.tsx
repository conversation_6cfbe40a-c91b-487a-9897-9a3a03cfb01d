import React from 'react';
import { useHistory, useLocation } from 'react-router-dom';
import { Button, Card } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import DatabaseDetail from './MySQLAnalysis/components/DatabaseDetail';
import { Database } from './MySQLAnalysis/types';

interface LocationState {
  databaseInfo: Database;
}

const DatabaseDetailPage: React.FC = () => {
  const { t } = useTranslation();
  const history = useHistory();
  const location = useLocation<LocationState>();
  const { databaseInfo } = location.state || {};

  const handleBack = () => {
    history.goBack();
  };

  return (
    <div>
      <Card 
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Button 
              type="text" 
              icon={<ArrowLeftOutlined />} 
              onClick={handleBack}
            >
              {t('返回')}
            </Button>
            <span>{databaseInfo?.name} {t('数据库详情')}</span>
          </div>
        }
      >
        {databaseInfo && (
          <DatabaseDetail 
            databaseInfo={databaseInfo}
            loading={false}
          />
        )}
      </Card>
    </div>
  );
};

export default DatabaseDetailPage;