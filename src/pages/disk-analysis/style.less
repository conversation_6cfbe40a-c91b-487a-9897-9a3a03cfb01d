.capacity-container {
  padding: 12px;
  
  .capacity-stats-card {
    margin-bottom: 12px;
  }
  
  .capacity-content-card {
    .capacity-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }
    
    .capacity-header-left {
      display: flex;
      align-items: center;
    }
    
    .ant-tabs-nav {
      margin-bottom: 12px;
    }
  }
  
  .capacity-trend-container {
    padding: 12px;
    
    h4 {
      margin-bottom: 6px;
    }
    
    p {
      margin-bottom: 16px;
      color: rgba(0, 0, 0, 0.45);
    }
  }
}

.capacity-growth-card {
  margin-top: 12px;
  margin-bottom: 12px;
}

.capacity-chart-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.capacity-summary-card {
  text-align: center;
}

// MySQL分析页面专属样式
.mysql-analysis-container {
  background-color: #f7f9fc;
  border-radius: 8px;
  padding: 16px;
  
  .mysql-page-header {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e8e8e8;
    
    .mysql-page-title {
      font-size: 16px;
      font-weight: 500;
      color: #1a1a1a;
      
      .anticon {
        margin-right: 6px;
        color: #1890ff;
        font-size: 14px;
      }
    }
  }
  
  .ant-card {
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    
    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      padding: 8px 16px; // 从12px 20px减少到8px 16px，让标题行更窄
      min-height: 36px; // 设置最小高度，确保标题行紧凑
      
      .ant-card-head-title {
        font-size: 13px; // 从14px减少到13px
        font-weight: 500;
        line-height: 1.4; // 设置行高，让文字垂直居中
        margin: 0; // 移除默认margin
      }
      
      .ant-card-extra {
        font-size: 12px; // 减小extra区域字体
        
        .ant-input {
          height: 28px; // 减小搜索框高度
          font-size: 12px;
        }
      }
    }
    
    .ant-card-body {
      padding: 16px 20px;
    }
  }
  
  // 专门针对数据库概览和数据库列表的Card标题优化
  .ant-card[title*="数据库概览"],
  .ant-card[title*="数据库列表"] {
    .ant-card-head {
      padding: 6px 16px; // 进一步减少内边距
      min-height: 32px; // 更小的最小高度
      
      .ant-card-head-title {
        font-size: 12px; // 更小的字体
        line-height: 1.3;
      }
    }
  }
  
  .mysql-ai-analysis {
    .mysql-ai-report-card,
    .mysql-trends-card {
      margin-bottom: 16px;
    }
  }
  
  // 刷新按钮行样式优化
  .mysql-refresh-section {
    margin-bottom: 12px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    min-height: 32px;
    
    .ant-btn {
      height: 32px;
      padding: 4px 12px;
      font-size: 13px;
      
      .anticon {
        font-size: 12px;
      }
    }
  }
  
  // 表格样式优化
  .ant-table {
    .ant-table-thead > tr > th {
      padding: 8px 12px;
      font-size: 13px;
    }
    
    .ant-table-tbody > tr > td {
      padding: 8px 12px;
      font-size: 13px;
    }
  }
  
  // Tab标签样式优化
  .ant-tabs {
    .ant-tabs-tab {
      padding: 8px 12px;
      font-size: 13px;
      
      .anticon {
        font-size: 12px;
        margin-right: 4px;
      }
    }
  }
  
  // 大文件列表表单样式
  .ant-card {
    .ant-card-head {
      padding: 6px 16px;
      min-height: 32px;
      
      .ant-card-head-title {
        font-size: 12px;
        font-weight: 500;
      }
    }
    
    .ant-card-body {
      padding: 12px;
    }
  }
  
  // 大文件列表特定样式
  .ant-table-thead > tr > th {
    padding: 6px 8px;
    font-size: 12px;
    height: 32px;
  }
  
  .ant-table-tbody > tr > td {
    padding: 6px 8px;
    font-size: 12px;
    height: 36px;
  }
  
  // 文件类型标签样式
  .ant-tag {
    margin: 0;
    font-size: 10px;
    padding: 0 4px;
    height: 20px;
    line-height: 18px;
  }
  
  // 操作按钮样式
  .ant-btn-link {
    padding: 0 4px;
    font-size: 12px;
    height: auto;
  }
}