import React from 'react';
import { DatabaseOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import PageLayout from '@/components/pageLayout';
import CapacityAnalysisPage from './components/CapacityAnalysisPage';
import './style.less';

// 主页面组件
const DiskAnalysisPage: React.FC = () => {
  const { t } = useTranslation();
  
  return (
    <PageLayout title={t('容量分析')} icon={<DatabaseOutlined />}>
      <CapacityAnalysisPage />
    </PageLayout>
  );
};

export default DiskAnalysisPage;