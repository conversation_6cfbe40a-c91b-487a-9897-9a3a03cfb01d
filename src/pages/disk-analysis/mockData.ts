// 存储统计数据
export const mockStorageStats = {
  totalStorage: 1024, // GB
  usedStorage: 768, // GB
  usedPercentage: 75, // %
  mysqlCount: 12,
  mysqlUsagePercentage: 65,
  containerCount: 28,
  containerUsagePercentage: 72,
  filesystemCount: 8,
  filesystemUsagePercentage: 83,
};

// MySQL数据库模拟数据
export const mockDatabases = [
  {
    id: 1,
    name: 'user_db',
    instance: 'mysql-prod-01',
    currentSize: 120,
    growthRate: 15,
    usagePercent: 85,
    estimatedExhaustionDate: '2023-12-15',
  },
  {
    id: 2,
    name: 'order_db',
    instance: 'mysql-prod-02',
    currentSize: 250,
    growthRate: 8,
    usagePercent: 65,
    estimatedExhaustionDate: '2024-03-20',
  },
  {
    id: 3,
    name: 'product_db',
    instance: 'mysql-prod-01',
    currentSize: 80,
    growthRate: 5,
    usagePercent: 45,
    estimatedExhaustionDate: '2024-06-10',
  },
  {
    id: 4,
    name: 'log_db',
    instance: 'mysql-prod-03',
    currentSize: 500,
    growthRate: 25,
    usagePercent: 92,
    estimatedExhaustionDate: '2023-11-05',
  },
  {
    id: 5,
    name: 'analytics_db',
    instance: 'mysql-prod-04',
    currentSize: 350,
    growthRate: 12,
    usagePercent: 78,
    estimatedExhaustionDate: '2024-01-15',
  },
];

// 容器模拟数据
export const mockContainers = [
  {
    id: 1,
    name: 'web-server-1',
    image: 'nginx:latest',
    status: 'running',
    diskUsage: 2.5,
    growthTrend: 'stable',
  },
  {
    id: 2,
    name: 'api-service',
    image: 'node:14',
    status: 'running',
    diskUsage: 4.8,
    growthTrend: 'moderate',
  },
  {
    id: 3,
    name: 'redis-cache',
    image: 'redis:6',
    status: 'running',
    diskUsage: 1.2,
    growthTrend: 'stable',
  },
  {
    id: 4,
    name: 'batch-processor',
    image: 'python:3.9',
    status: 'stopped',
    diskUsage: 8.5,
    growthTrend: 'rapid',
  },
  {
    id: 5,
    name: 'monitoring-agent',
    image: 'prometheus:latest',
    status: 'running',
    diskUsage: 6.2,
    growthTrend: 'moderate',
  },
];

// 磁盘使用模拟数据
export const mockDisks = [
  {
    id: 1,
    path: '/dev/sda1',
    mountPoint: '/',
    totalSize: 500,
    usedSize: 350,
    usagePercent: 70,
    growthRate: 5,
    estimatedFullDate: '2024-02-15',
  },
  {
    id: 2,
    path: '/dev/sdb1',
    mountPoint: '/data',
    totalSize: 1000,
    usedSize: 850,
    usagePercent: 85,
    growthRate: 12,
    estimatedFullDate: '2023-12-10',
  },
  {
    id: 3,
    path: '/dev/sdc1',
    mountPoint: '/backup',
    totalSize: 2000,
    usedSize: 1200,
    usagePercent: 60,
    growthRate: 3,
    estimatedFullDate: '2024-05-20',
  },
  {
    id: 4,
    path: '/dev/sdd1',
    mountPoint: '/logs',
    totalSize: 500,
    usedSize: 450,
    usagePercent: 90,
    growthRate: 18,
    estimatedFullDate: '2023-11-05',
  },
  {
    id: 5,
    path: '/dev/sde1',
    mountPoint: '/tmp',
    totalSize: 200,
    usedSize: 50,
    usagePercent: 25,
    growthRate: 2,
    estimatedFullDate: '2024-08-30',
  },
];