import React, { useState } from 'react';
import { Button, Card, Tabs, Input, Row, Col, Statistic, Tooltip } from 'antd';
import { 
  SearchOutlined, 
  PlusOutlined, 
  QuestionCircleOutlined, 
  ArrowLeftOutlined 
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { Typography } from 'antd';
import QueueTable from './QueueTable';
import ConsumerTable from './ConsumerTable';
import { mockQueues, mockConsumers } from '../mockData';

const { TabPane } = Tabs;
const { Title } = Typography;

interface RabbitMQPageProps {
  onBack: () => void;
}

const RabbitMQPage: React.FC<RabbitMQPageProps> = ({ onBack }) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('queues');
  const [searchText, setSearchText] = useState('');
  
  return (
    <div className="mq-container">
      <div className="mq-page-header">
        <Button 
          type="link" 
          icon={<ArrowLeftOutlined />} 
          onClick={onBack}
        >
          {t('返回选择')}
        </Button>
        <Title level={3}>{t('RabbitMQ接口')}</Title>
      </div>
      
      <Card className="mq-stats-card">
        <Row gutter={16}>
          <Col span={6}>
            <Statistic 
              title={t('总队列数')} 
              value={mockQueues.length} 
              suffix={
                <Tooltip title={t('系统中的消息队列总数')}>
                  <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
              }
            />
          </Col>
          <Col span={6}>
            <Statistic 
              title={t('活跃队列')} 
              value={mockQueues.filter(q => q.status === 'active').length} 
              valueStyle={{ color: '#3f8600' }}
            />
          </Col>
          <Col span={6}>
            <Statistic 
              title={t('总消费者数')} 
              value={mockConsumers.length}
            />
          </Col>
          <Col span={6}>
            <Statistic 
              title={t('总消息数')} 
              value={mockQueues.reduce((sum, q) => sum + q.messages, 0)}
            />
          </Col>
        </Row>
      </Card>
      
      <Card className="mq-content-card">
        <div className="mq-header">
          <Input
            placeholder={t('搜索名称')}
            prefix={<SearchOutlined />}
            style={{ width: 250 }}
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
          />
          <Button type="primary" icon={<PlusOutlined />}>
            {activeTab === 'queues' ? t('新建队列') : t('新建消费者')}
          </Button>
        </div>
        
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={t('队列')} key="queues">
            <QueueTable searchText={searchText} />
          </TabPane>
          <TabPane tab={t('消费者')} key="consumers">
            <ConsumerTable searchText={searchText} />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default RabbitMQPage;