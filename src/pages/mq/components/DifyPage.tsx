import React, { useState } from 'react';
import { Card, Table, Tag, Space, Input, Button, Tabs, Row, Col, Statistic, Typography, Tooltip, Progress, message } from 'antd';
import { SearchOutlined, ReloadOutlined, PlusOutlined, QuestionCircleOutlined, ClockCircleOutlined, HddOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { mockQueues, mockConsumers, statusColors, mockServerStats } from '../mockData';
import { MemoryIcon } from '../icons'; // 导入图标组件

const { Title } = Typography;
const { TabPane } = Tabs;

// 移除 onBack 属性
interface DifyPageProps {
  // 不再需要 onBack 属性
}

const DifyPage: React.FC<DifyPageProps> = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('rabbitmq');
  const [searchText, setSearchText] = useState('');
  const [mqTabActive, setMqTabActive] = useState('queues');
  const [queues, setQueues] = useState(mockQueues);
  const [consumers, setConsumers] = useState(mockConsumers);
  const [loading, setLoading] = useState(false);
  
  // 过滤数据
  const filteredQueues = queues.filter(item => 
    item.name.toLowerCase().includes(searchText.toLowerCase())
  );
  
  const filteredConsumers = consumers.filter(item => 
    item.name.toLowerCase().includes(searchText.toLowerCase()) || 
    item.queue.toLowerCase().includes(searchText.toLowerCase())
  );
  
  // 刷新数据函数
  const refreshData = () => {
    setLoading(true);
    
    // 模拟API请求延迟
    setTimeout(() => {
      // 在实际应用中，这里应该是从API获取最新数据
      // 目前使用模拟数据，随机修改一些值以模拟刷新效果
      const updatedQueues = queues.map(queue => {
        const totalMessages = Math.floor(Math.random() * 1000);
        const readyMessages = Math.floor(totalMessages * 0.7); // 假设70%是Ready状态
        const unackedMessages = totalMessages - readyMessages; // 剩余的是Unacked状态
        const consumersCount = Math.floor(Math.random() * 10);
        
        // 计算消费速率 (条/秒)
        // 在实际应用中，这应该是从监控系统获取的实际数据
        // 这里使用模拟数据：每个消费者每秒处理0.5-5条消息
        const consumptionRate = consumersCount > 0 
          ? consumersCount * (0.5 + Math.random() * 4.5) 
          : 0;
        
        return {
          ...queue,
          messages: totalMessages,
          readyMessages: readyMessages,
          unackedMessages: unackedMessages,
          consumers: consumersCount,
          consumptionRate: consumptionRate,
        };
      });
      
      const updatedConsumers = consumers.map(consumer => ({
        ...consumer,
        messagesProcessed: Math.floor(Math.random() * 5000),
        lastActive: new Date().toLocaleString()
      }));
      
      setQueues(updatedQueues);
      setConsumers(updatedConsumers);
      setLoading(false);
      message.success(t('数据已刷新'));
    }, 800);
  };
  
  // 队列表格列定义
  const queueColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: t('队列名称'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('类型'),
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag>{type.toUpperCase()}</Tag>
      ),
    },
    {
      title: t('状态'),
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={statusColors[status] || 'default'}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: t('总消息数'),
      dataIndex: 'messages',
      key: 'messages',
      sorter: (a, b) => a.messages - b.messages,
      defaultSortOrder: 'descend',
    },
    {
      title: t('Ready消息'),
      dataIndex: 'readyMessages',
      key: 'readyMessages',
      sorter: (a, b) => (a.readyMessages || 0) - (b.readyMessages || 0),
    },
    {
      title: t('Unacked消息'),
      dataIndex: 'unackedMessages',
      key: 'unackedMessages',
      sorter: (a, b) => (a.unackedMessages || 0) - (b.unackedMessages || 0),
    },
    {
      title: t('消费者数'),
      dataIndex: 'consumers',
      key: 'consumers',
    },
    {
      title: t('消费速率 (条/秒)'),
      dataIndex: 'consumptionRate',
      key: 'consumptionRate',
      sorter: (a, b) => (a.consumptionRate || 0) - (b.consumptionRate || 0),
      render: (rate) => rate ? rate.toFixed(2) : '0.00',
    },
    {
      title: t('预计完成时间'),
      dataIndex: 'estimatedCompletionTime',
      key: 'estimatedCompletionTime',
      render: (_, record) => {
        if (!record.consumptionRate || record.consumptionRate === 0 || !record.readyMessages || record.readyMessages === 0) {
          return <span>-</span>;
        }
        
        // 计算预计完成时间（秒）
        const timeInSeconds = record.readyMessages / record.consumptionRate;
        
        // 格式化显示
        if (timeInSeconds < 60) {
          return <span>{Math.ceil(timeInSeconds)} {t('秒')}</span>;
        } else if (timeInSeconds < 3600) {
          return <span>{Math.ceil(timeInSeconds / 60)} {t('分钟')}</span>;
        } else if (timeInSeconds < 86400) {
          return <span>{Math.ceil(timeInSeconds / 3600)} {t('小时')}</span>;
        } else {
          return <span>{Math.ceil(timeInSeconds / 86400)} {t('天')}</span>;
        }
      },
    },
    {
      title: t('操作'),
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <a>{t('清空队列')}</a>
          <a>{t('删除重建')}</a>
        </Space>
      ),
    },
  ];
  
  // 消费者表格列定义
  const consumerColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: t('消费者名称'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('队列'),
      dataIndex: 'queue',
      key: 'queue',
    },
    {
      title: t('状态'),
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={statusColors[status] || 'default'}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: t('已处理消息'),
      dataIndex: 'messagesProcessed',
      key: 'messagesProcessed',
    },
    {
      title: t('最后活动时间'),
      dataIndex: 'lastActive',
      key: 'lastActive',
    },
  ];
  
  // 渲染RabbitMQ管理选项卡
  const renderRabbitMQTab = () => {
    return (
      <>
        <Card className="mq-stats-card">
          <Row gutter={16}>
            <Col span={8}>
              <Statistic 
                title={t('总消息数')} 
                value={mockQueues.reduce((sum, q) => sum + q.messages, 0)}
              />
            </Col>
            <Col span={8}>
              <Statistic 
                title={t('Ready消息')} 
                value={mockQueues.reduce((sum, q) => sum + (q.readyMessages || 0), 0)}
                valueStyle={{ color: '#1890ff' }}
                suffix={
                  <Tooltip title={t('等待被消费的消息数量')}>
                    <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                  </Tooltip>
                }
              />
            </Col>
            <Col span={8}>
              <Statistic 
                title={t('Unacked消息')} 
                value={mockQueues.reduce((sum, q) => sum + (q.unackedMessages || 0), 0)}
                valueStyle={{ color: '#faad14' }}
                suffix={
                  <Tooltip title={t('已发送但未被确认的消息数量')}>
                    <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                  </Tooltip>
                }
              />
            </Col>
          </Row>
          <Row gutter={16} style={{ marginTop: 16 }}>
            <Col span={8}>
              <Statistic 
                title={t('Memory')} 
                value={mockServerStats.memory}
                valueStyle={{ color: '#1890ff' }}
                prefix={<MemoryIcon style={{ marginRight: 8 }} />}
                suffix={
                  <>
                    MB
                    <Tooltip title={t('RabbitMQ 服务器当前内存使用量')}>
                      <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                    </Tooltip>
                  </>
                }
              />
            </Col>
            <Col span={8}>
              <Statistic 
                title={t('Disk Space')} 
                value={mockServerStats.diskSpace}
                valueStyle={{ color: mockServerStats.diskSpace < 10 ? '#faad14' : '#52c41a' }}
                prefix={<HddOutlined style={{ marginRight: 8 }} />}
                suffix={
                  <>
                    GB
                    <Tooltip title={t('RabbitMQ 服务器可用磁盘空间')}>
                      <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                    </Tooltip>
                  </>
                }
              />
            </Col>
            <Col span={8}>
              <Statistic 
                title={t('Uptime')} 
                value={mockServerStats.uptime}
                valueStyle={{ color: '#52c41a' }}
                prefix={<ClockCircleOutlined style={{ marginRight: 8 }} />}
                suffix={
                  <>
                    {t('天')}
                    <Tooltip title={t('RabbitMQ 服务器已运行时间')}>
                      <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                    </Tooltip>
                  </>
                }
              />
            </Col>
          </Row>
        </Card>
        
        <Card className="mq-content-card">
          <div className="mq-header">
            <div className="mq-header-left">
              <Input
                placeholder={t('搜索名称')}
                prefix={<SearchOutlined />}
                style={{ width: 250 }}
                value={searchText}
                onChange={e => setSearchText(e.target.value)}
              />
              <Button 
                icon={<ReloadOutlined />} 
                onClick={refreshData} 
                loading={loading}
                style={{ marginLeft: 8 }}
              >
                {t('刷新')}
              </Button>
            </div>
            {/* 移除了新建按钮 */}
          </div>
          
          <Tabs activeKey={mqTabActive} onChange={setMqTabActive}>
            <TabPane tab={t('队列')} key="queues">
              <Table
                columns={queueColumns}
                dataSource={filteredQueues}
                rowKey="id"
                pagination={{ pageSize: 10 }}
                loading={loading}
              />
            </TabPane>
            <TabPane tab={t('消费者')} key="consumers">
              <Table
                columns={consumerColumns}
                dataSource={filteredConsumers}
                rowKey="id"
                pagination={{ pageSize: 10 }}
                loading={loading}
              />
            </TabPane>
          </Tabs>
        </Card>
      </>
    );
  };
  
  // 渲染AI助手选项卡
  const renderAIAssistantTab = () => {
    return (
      <Card className="mq-dify-ai-card">
        <div className="mq-ai-assistant">
          <div className="mq-ai-header">
            <Title level={4}>{t('RabbitMQ AI助手')}</Title>
            <p>{t('使用AI助手帮助您管理和优化RabbitMQ')}</p>
          </div>
          
          <div className="mq-ai-features">
            <Row gutter={[16, 16]}>
              <Col span={8}>
                <Card className="mq-ai-feature-card">
                  <h4>{t('队列性能分析')}</h4>
                  <p>{t('分析队列性能并提供优化建议')}</p>
                  <Button type="primary">{t('开始分析')}</Button>
                </Card>
              </Col>
              <Col span={8}>
                <Card className="mq-ai-feature-card">
                  <h4>{t('消息路由优化')}</h4>
                  <p>{t('优化消息路由策略，提高处理效率')}</p>
                  <Button type="primary">{t('开始优化')}</Button>
                </Card>
              </Col>
              <Col span={8}>
                <Card className="mq-ai-feature-card">
                  <h4>{t('异常检测')}</h4>
                  <p>{t('检测并分析队列异常情况')}</p>
                  <Button type="primary">{t('开始检测')}</Button>
                </Card>
              </Col>
            </Row>
          </div>
          
          <div className="mq-ai-chat">
            <Card title={t('与AI助手对话')} className="mq-ai-chat-card">
              <div className="mq-ai-chat-messages">
                <div className="mq-ai-message ai">
                  <p>{t('您好！我是RabbitMQ AI助手，有什么可以帮助您的吗？')}</p>
                </div>
              </div>
              <div className="mq-ai-chat-input">
                <Input.TextArea 
                  rows={3} 
                  placeholder={t('输入您的问题，例如：如何优化队列性能？')}
                />
                <Button type="primary">{t('发送')}</Button>
              </div>
            </Card>
          </div>
        </div>
      </Card>
    );
  };
  
  return (
    <div className="mq-dify-container">
      <Tabs activeKey={activeTab} onChange={setActiveTab} className="mq-dify-tabs">
        <TabPane tab={t('RabbitMQ管理')} key="rabbitmq">
          {renderRabbitMQTab()}
        </TabPane>
        <TabPane tab={t('AI助手')} key="ai-assistant">
          {renderAIAssistantTab()}
        </TabPane>
      </Tabs>
    </div>
  );
};

export default DifyPage;