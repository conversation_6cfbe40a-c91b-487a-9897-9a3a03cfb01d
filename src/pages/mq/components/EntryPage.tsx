import React from 'react';
import { Card, Row, Col, Typography } from 'antd';
import { RobotOutlined, ApiOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Paragraph } = Typography;

interface EntryPageProps {
  onSelectDify: () => void;
  onSelectRabbitMQ: () => void;
}

const EntryPage: React.FC<EntryPageProps> = ({ onSelectDify, onSelectRabbitMQ }) => {
  const { t } = useTranslation();
  
  return (
    <div className="mq-entry-container">
      <Title level={2}>{t('选择MQ接入方式')}</Title>
      <Paragraph>{t('请选择您希望使用的消息队列接入方式')}</Paragraph>
      
      <Row gutter={24} className="mq-entry-cards">
        <Col span={12}>
          <Card 
            hoverable 
            className="mq-entry-card"
            onClick={onSelectDify}
          >
            <div className="mq-entry-card-header">
              <RobotOutlined className="mq-entry-card-icon" />
              <Title level={3}>{t('Dify接入方式')}</Title>
            </div>
            <Paragraph>{t('通过Dify API接入，快速集成AI能力')}</Paragraph>
            <div className="mq-entry-card-features">
              <ul>
                <li><CheckCircleOutlined /> {t('简单易用的API配置')}</li>
                <li><CheckCircleOutlined /> {t('内置应用管理')}</li>
              </ul>
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card 
            hoverable 
            className="mq-entry-card"
            onClick={onSelectRabbitMQ}
          >
            <div className="mq-entry-card-header">
              <ApiOutlined className="mq-entry-card-icon" />
              <Title level={3}>{t('RabbitMQ接口方式')}</Title>
            </div>
            <Paragraph>{t('使用标准RabbitMQ接口，灵活管理消息队列')}</Paragraph>
            <div className="mq-entry-card-features">
              <ul>
                <li><CheckCircleOutlined /> {t('完整的队列管理')}</li>
                <li><CheckCircleOutlined /> {t('消费者监控')}</li>
                <li><CheckCircleOutlined /> {t('高级消息路由')}</li>
              </ul>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default EntryPage;