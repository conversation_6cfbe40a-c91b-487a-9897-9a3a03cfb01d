import React from 'react';
import { Table, Tag, Space } from 'antd';
import { useTranslation } from 'react-i18next';
import { mockConsumers, statusColors } from '../mockData';

interface ConsumerTableProps {
  searchText: string;
}

const ConsumerTable: React.FC<ConsumerTableProps> = ({ searchText }) => {
  const { t } = useTranslation();
  
  // 过滤数据
  const filteredConsumers = mockConsumers.filter(item => 
    item.name.toLowerCase().includes(searchText.toLowerCase()) || 
    item.queue.toLowerCase().includes(searchText.toLowerCase())
  );
  
  // 消费者表格列定义
  const consumerColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: t('消费者名称'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('队列'),
      dataIndex: 'queue',
      key: 'queue',
    },
    {
      title: t('状态'),
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={statusColors[status] || 'default'}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: t('已处理消息'),
      dataIndex: 'messagesProcessed',
      key: 'messagesProcessed',
    },
    {
      title: t('最后活动时间'),
      dataIndex: 'lastActive',
      key: 'lastActive',
    },
    {
      title: t('操作'),
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <a>{t('查看详情')}</a>
          <a>{record.status === 'connected' ? t('断开连接') : t('连接')}</a>
        </Space>
      ),
    },
  ];
  
  return (
    <Table
      columns={consumerColumns}
      dataSource={filteredConsumers}
      rowKey="id"
      pagination={{ pageSize: 10 }}
    />
  );
};

export default ConsumerTable;