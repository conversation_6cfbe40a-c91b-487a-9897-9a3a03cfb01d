import React from 'react';
import { Table, Tag, Space } from 'antd';
import { useTranslation } from 'react-i18next';
import { mockQueues, statusColors } from '../mockData';

interface QueueTableProps {
  searchText: string;
}

const QueueTable: React.FC<QueueTableProps> = ({ searchText }) => {
  const { t } = useTranslation();
  
  // 过滤数据
  const filteredQueues = mockQueues.filter(item => 
    item.name.toLowerCase().includes(searchText.toLowerCase())
  );
  
  // 队列表格列定义
  const queueColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: t('队列名称'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('类型'),
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag>{type.toUpperCase()}</Tag>
      ),
    },
    {
      title: t('状态'),
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={statusColors[status] || 'default'}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: t('消息数'),
      dataIndex: 'messages',
      key: 'messages',
      sorter: (a, b) => a.messages - b.messages,
      defaultSortOrder: 'descend', // 默认按照消息数从多到少排序
    },
    {
      title: t('消费者数'),
      dataIndex: 'consumers',
      key: 'consumers',
    },
    // 消费速度列
    {
      title: t('消费速度'),
      key: 'consumptionRate',
      render: (_, record) => {
        // 模拟消费速度数据 (消息/秒)
        const rate = record.status === 'active' && record.consumers > 0 
          ? Math.floor(Math.random() * 50) + 10 
          : 0;
        
        return rate > 0 
          ? <span>{rate} {t('消息/秒')}</span>
          : <span>-</span>;
      },
    },
    // 预计剩余时长列
    {
      title: t('预计剩余时长'),
      key: 'remainingTime',
      render: (_, record) => {
        // 模拟消费速度数据 (消息/秒)
        const rate = record.status === 'active' && record.consumers > 0 
          ? Math.floor(Math.random() * 50) + 10 
          : 0;
        
        if (rate > 0 && record.messages > 0) {
          // 计算剩余时间（秒）
          const remainingSeconds = Math.ceil(record.messages / rate);
          
          // 格式化剩余时间
          if (remainingSeconds < 60) {
            return <span>{remainingSeconds} {t('秒')}</span>;
          } else if (remainingSeconds < 3600) {
            const minutes = Math.ceil(remainingSeconds / 60);
            return <span>{minutes} {t('分钟')}</span>;
          } else if (remainingSeconds < 86400) {
            const hours = Math.ceil(remainingSeconds / 3600);
            return <span>{hours} {t('小时')}</span>;
          } else {
            const days = Math.ceil(remainingSeconds / 86400);
            return <span>{days} {t('天')}</span>;
          }
        }
        
        return <span>-</span>;
      },
    },
    {
      title: t('创建时间'),
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: t('操作'),
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <a>{t('删除')}</a>
        </Space>
      ),
    },
  ];
  
  return (
    <Table
      columns={queueColumns}
      dataSource={filteredQueues}
      rowKey="id"
      pagination={{ pageSize: 10 }}
      defaultSortOrder="descend"
      sortDirections={['descend', 'ascend']}
    />
  );
};

export default QueueTable;