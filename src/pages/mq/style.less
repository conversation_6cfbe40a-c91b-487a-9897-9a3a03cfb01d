.mq-container {
  padding: 16px;
  
  .mq-stats-card {
    margin-bottom: 16px;
  }
  
  .mq-content-card {
    .mq-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }
    
    .mq-header-left {
      display: flex;
      align-items: center;
    }
    
    .ant-tabs-nav {
      margin-bottom: 16px;
    }
  }
}

// 入口选择页面样式
.mq-entry-container {
  padding: 24px;
  
  h2 {
    margin-bottom: 16px;
  }
  
  .mq-entry-cards {
    margin-top: 24px;
  }
  
  .mq-entry-card {
    height: 100%;
    transition: all 0.3s;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .mq-entry-card-header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      
      .mq-entry-card-icon {
        font-size: 24px;
        margin-right: 12px;
        color: #1890ff;
      }
      
      h3 {
        margin: 0;
      }
    }
    
    .mq-entry-card-features {
      margin-top: 16px;
      
      ul {
        padding-left: 0;
        list-style: none;
        
        li {
          margin-bottom: 8px;
          
          .anticon {
            color: #52c41a;
            margin-right: 8px;
          }
        }
      }
    }
  }
}

// Dify接入页面样式
.mq-dify-container {
  padding: 16px;
  
  .mq-page-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    
    h3 {
      margin: 0 0 0 8px;
    }
  }
  
  .mq-dify-tabs {
    .ant-tabs-nav {
      margin-bottom: 16px;
    }
  }
  
  .mq-dify-config-card,
  .mq-dify-apps-card,
  .mq-dify-rabbitmq-config-card {
    margin-bottom: 16px;
  }
  
  .mq-form-item {
    margin-bottom: 16px;
    
    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
    }
  }
  
  .mq-form-actions {
    margin-top: 16px;
    
    button {
      margin-right: 8px;
    }
  }
  
  .mq-stats-card {
    margin-bottom: 16px;
  }
  
  .mq-content-card {
    .mq-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
    }
    
    .ant-tabs-nav {
      margin-bottom: 16px;
    }
  }
  
  .mq-dify-ai-card {
    .mq-ai-header {
      margin-bottom: 24px;
      
      h4 {
        margin-bottom: 8px;
      }
    }
    
    .mq-ai-features {
      margin-bottom: 24px;
      
      .mq-ai-feature-card {
        height: 100%;
        text-align: center;
        
        h4 {
          margin-bottom: 8px;
        }
        
        p {
          margin-bottom: 16px;
          min-height: 40px;
        }
      }
    }
    
    .mq-ai-chat {
      .mq-ai-chat-card {
        .mq-ai-chat-messages {
          max-height: 300px;
          overflow-y: auto;
          margin-bottom: 16px;
          
          .mq-ai-message {
            padding: 8px 12px;
            margin-bottom: 8px;
            border-radius: 4px;
            
            &.ai {
              background-color: #f0f2f5;
            }
            
            &.user {
              background-color: #e6f7ff;
              text-align: right;
            }
            
            p {
              margin: 0;
            }
          }
        }
        
        .mq-ai-chat-input {
          display: flex;
          
          textarea {
            flex: 1;
            margin-right: 8px;
          }
        }
      }
    }
  }
}

// RabbitMQ接口页面样式
.mq-rabbitmq-container {
  padding: 16px;
  
  .mq-page-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    
    h3 {
      margin: 0 0 0 8px;
    }
  }
}