import request from '@/utils/request';
import api from '@/utils/api';

// 获取队列列表
export const getQueueList = (params: any) => {
  return request(`${api.mq}/queues`, {
    method: 'GET',
    params,
  });
};

// 创建队列
export const createQueue = (data: any) => {
  return request(`${api.mq}/queues`, {
    method: 'POST',
    body: JSON.stringify(data),
  });
};

// 获取消费者列表
export const getConsumerList = (params: any) => {
  return request(`${api.mq}/consumers`, {
    method: 'GET',
    params,
  });
};

// 创建消费者
export const createConsumer = (data: any) => {
  return request(`${api.mq}/consumers`, {
    method: 'POST',
    body: JSON.stringify(data),
  });
};

// 连接/断开消费者
export const toggleConsumerConnection = (id: number, connect: boolean) => {
  return request(`${api.mq}/consumers/${id}/${connect ? 'connect' : 'disconnect'}`, {
    method: 'POST',
  });
};