// 如果文件不存在，请创建此文件
import { Tag } from 'antd';

// 状态颜色映射
export const statusColors = {
  active: 'green',
  inactive: 'orange',
  error: 'red',
  connected: 'green',
  disconnected: 'red',
};

// 模拟服务器统计数据
export const mockServerStats = {
  memory: 256, // MB
  diskSpace: 42.5, // GB
  uptime: 15, // 天
  version: '3.9.13',
  erlangVersion: '24.3.4',
  clusterNodes: 1,
  connections: 12,
  channels: 24,
  exchanges: 8,
  virtualHosts: 1
};

// 模拟队列数据
export const mockQueues = [
  {
    id: 1,
    name: 'order-processing',
    type: 'direct',
    status: 'active',
    messages: 245,
    readyMessages: 180,
    unackedMessages: 65,
    consumers: 5,
    createdAt: '2023-05-10 08:30:22',
    updatedAt: '2023-05-15 14:22:10',
    totalMessages: 1000,
    durable: true,
    autoDelete: false,
    exclusive: false,
    maxPriority: 10,
    exchange: 'order-exchange',
    routingKey: 'order.created'
  },
  {
    id: 2,
    name: 'notification-service',
    type: 'fanout',
    status: 'active',
    messages: 78,
    readyMessages: 50,
    unackedMessages: 28,
    consumers: 3,
    createdAt: '2023-05-11 10:15:45',
    updatedAt: '2023-05-15 13:40:18',
    totalMessages: 500,
    durable: true,
    autoDelete: false,
    exclusive: false,
    maxPriority: null,
    exchange: 'notification-exchange',
    routingKey: ''
  },
  {
    id: 3,
    name: 'email-queue',
    type: 'direct',
    status: 'inactive',
    messages: 0,
    readyMessages: 0,
    unackedMessages: 0,
    consumers: 0,
    createdAt: '2023-05-12 09:20:33',
    updatedAt: '2023-05-14 11:30:55',
    totalMessages: 200,
    durable: true,
    autoDelete: true,
    exclusive: false,
    maxPriority: null,
    exchange: 'email-exchange',
    routingKey: 'email.send'
  },
  {
    id: 4,
    name: 'log-processing',
    type: 'topic',
    status: 'active',
    messages: 532,
    readyMessages: 400,
    unackedMessages: 132,
    consumers: 2,
    createdAt: '2023-05-09 14:45:10',
    updatedAt: '2023-05-15 15:10:22',
    totalMessages: 1500,
    durable: true,
    autoDelete: false,
    exclusive: false,
    maxPriority: 5,
    exchange: 'log-exchange',
    routingKey: 'log.#'
  },
  {
    id: 5,
    name: 'payment-processing',
    type: 'direct',
    status: 'error',
    messages: 45,
    readyMessages: 45,
    unackedMessages: 0,
    consumers: 0,
    createdAt: '2023-05-13 11:25:40',
    updatedAt: '2023-05-15 09:15:30',
    totalMessages: 100,
    durable: true,
    autoDelete: false,
    exclusive: false,
    maxPriority: null,
    exchange: 'payment-exchange',
    routingKey: 'payment.process'
  }
];

// 模拟消费者数据
export const mockConsumers = [
  {
    id: 1,
    name: 'order-processor-1',
    queue: 'order-processing',
    status: 'connected',
    messagesProcessed: 1245,
    lastActive: '2023-05-15 15:30:22'
  },
  {
    id: 2,
    name: 'order-processor-2',
    queue: 'order-processing',
    status: 'connected',
    messagesProcessed: 1120,
    lastActive: '2023-05-15 15:29:45'
  },
  {
    id: 3,
    name: 'notification-handler-1',
    queue: 'notification-service',
    status: 'connected',
    messagesProcessed: 890,
    lastActive: '2023-05-15 15:28:10'
  },
  {
    id: 4,
    name: 'log-processor-1',
    queue: 'log-processing',
    status: 'connected',
    messagesProcessed: 2340,
    lastActive: '2023-05-15 15:30:05'
  },
  {
    id: 5,
    name: 'email-sender-1',
    queue: 'email-queue',
    status: 'disconnected',
    messagesProcessed: 450,
    lastActive: '2023-05-14 18:45:30'
  }
];