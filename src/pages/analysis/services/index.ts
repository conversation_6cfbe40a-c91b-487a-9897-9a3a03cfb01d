import request from '@/utils/request';
import api from '@/utils/api';

// 获取诊断列表
export const getDiagnosisList = (params: any) => {
  return request(`${api.analysis}/diagnosis`, {
    method: 'GET',
    params,
  });
};

// 创建诊断
export const createDiagnosis = (data: any) => {
  return request(`${api.analysis}/diagnosis`, {
    method: 'POST',
    body: JSON.stringify(data),
  });
};

// 获取诊断详情
export const getDiagnosisDetail = (id: number) => {
  return request(`${api.analysis}/diagnosis/${id}`, {
    method: 'GET',
  });
};

// 执行诊断
export const executeDiagnosis = (id: number) => {
  return request(`${api.analysis}/diagnosis/${id}/execute`, {
    method: 'POST',
  });
};