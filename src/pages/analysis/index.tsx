/*
 * Copyright 2022 Nightingale Team
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Button, Input, Select, Table, Tag, Space, Empty } from 'antd';
import { SearchOutlined, RobotOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import PageLayout from '@/components/pageLayout';
import './style.less';

const { Option } = Select;

// 模拟数据
const mockData = [
  {
    id: 1,
    name: '服务器CPU使用率异常',
    status: 'warning',
    type: 'system',
    createTime: '2023-05-15 10:30:45',
    updateTime: '2023-05-15 11:20:30',
  },
  {
    id: 2,
    name: '数据库连接超时',
    status: 'critical',
    type: 'database',
    createTime: '2023-05-14 08:15:22',
    updateTime: '2023-05-14 09:45:10',
  },
  {
    id: 3,
    name: '网络延迟增高',
    status: 'info',
    type: 'network',
    createTime: '2023-05-13 14:22:36',
    updateTime: '2023-05-13 15:10:05',
  },
];

const AnalysisPage: React.FC = () => {
  const { t } = useTranslation();
  const [searchText, setSearchText] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [data, setData] = useState(mockData);

  // 状态标签颜色映射
  const statusColors = {
    info: 'blue',
    warning: 'orange',
    critical: 'red',
  };

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: t('诊断名称'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('状态'),
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={statusColors[status] || 'default'}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: t('类型'),
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: t('创建时间'),
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: t('更新时间'),
      dataIndex: 'updateTime',
      key: 'updateTime',
    },
    {
      title: t('操作'),
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <a>{t('查看详情')}</a>
          <a>{t('执行诊断')}</a>
        </Space>
      ),
    },
  ];

  // 过滤数据
  useEffect(() => {
    let filteredData = mockData;
    
    if (searchText) {
      filteredData = filteredData.filter(item => 
        item.name.toLowerCase().includes(searchText.toLowerCase())
      );
    }
    
    if (filterType !== 'all') {
      filteredData = filteredData.filter(item => item.type === filterType);
    }
    
    setData(filteredData);
  }, [searchText, filterType]);

  return (
    <PageLayout title={t('智能诊断')} icon={<RobotOutlined />}>
      <div className="analysis-container">
        <Card className="analysis-search-card">
          <Row gutter={16} align="middle">
            <Col span={8}>
              <Input
                placeholder={t('搜索诊断名称')}
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={e => setSearchText(e.target.value)}
              />
            </Col>
            <Col span={6}>
              <Select
                style={{ width: '100%' }}
                placeholder={t('选择类型')}
                value={filterType}
                onChange={value => setFilterType(value)}
              >
                <Option value="all">{t('全部')}</Option>
                <Option value="system">{t('系统')}</Option>
                <Option value="database">{t('数据库')}</Option>
                <Option value="network">{t('网络')}</Option>
              </Select>
            </Col>
            <Col span={10} style={{ textAlign: 'right' }}>
              <Button type="primary">{t('新建诊断')}</Button>
            </Col>
          </Row>
        </Card>
        
        <Card className="analysis-table-card">
          <Table
            columns={columns}
            dataSource={data}
            rowKey="id"
            pagination={{ pageSize: 10 }}
            locale={{
              emptyText: <Empty description={t('暂无诊断数据')} />
            }}
          />
        </Card>
      </div>
    </PageLayout>
  );
};

export default AnalysisPage;