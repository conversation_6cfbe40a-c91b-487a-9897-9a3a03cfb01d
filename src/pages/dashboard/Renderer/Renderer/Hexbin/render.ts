/*
 * Copyright 2022 Nightingale Team
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
import { min, select, event } from 'd3';
import * as d3 from 'd3'; // 导入完整的 d3 库以确保兼容性
import { hexbin as d3Hexbin } from 'd3-hexbin';
import _ from 'lodash';
import { bestFitElemCountPerRow, getTextSizeForWidthAndHeight, getMapColumnsAndRows } from './utils';

const xmlns = 'http://www.w3.org/2000/svg';
const minFont = 6;
function getPlaceHolderElems(rows, columns, len, radius: number) {
  let points: any[] = [];
  for (let i = 0, count = 0; i < rows; i++) {
    for (let j = 0; j < columns && count <= len - 1; j++, count++) {
      points.push([radius * j * 1.75, radius * i * 1.5]);
    }
  }
  return points;
}
function computeTextFontSize(text: string, linesToDisplay: number, textAreaWidth: number, textAreaHeight: number): number {
  return getTextSizeForWidthAndHeight(text, textAreaWidth, textAreaHeight / linesToDisplay);
}
function getHexbinHeight(mapRows, hexRadius) {
  let count = 0;
  for (let i = 1; i <= mapRows; i++) {
    if (i === mapRows && i !== 1) {
      count += 1.5;
    } else if (i % 2 === 1) {
      count += 2;
    } else {
      count += 1;
    }
  }
  return count * hexRadius;
}

const div = select('body')
  .append(function () {
    return document.createElement('div');
  })
  .attr('class', 'hexbin-tooltip')
  .style('opacity', 0);

function renderHoneyComb(svgGroup, data, { width, height, fontAutoScale = true, fontSize = 12, themeMode, textMode }) {
  console.log('renderHoneyComb: 开始渲染蜂窝图', { width, height, dataLength: data.length });
  
  console.log('准备创建 hexbin 图形');
  
  try {
    const { columns: mapColumns, rows: mapRows } = getMapColumnsAndRows(width, height, data.length);
    const hexRadius = Math.floor(min([width / ((mapColumns + 0.5) * Math.sqrt(3)), height / ((mapRows + 1 / 3) * 1.5), width / 7]));
    const hexbinWidth = Math.sin((60 * Math.PI) / 180) * hexRadius * 2;
    const points = getPlaceHolderElems(mapRows, mapColumns, data.length, hexRadius);
    let adjustedOffSetX = (width - hexbinWidth * mapColumns) / 2 + hexbinWidth / 2;
    if (points.length >= mapColumns * 2) {
      adjustedOffSetX = (width - hexbinWidth * mapColumns - hexbinWidth / 2) / 2 + hexbinWidth / 2;
    }
    const adjustedOffSetY = (height - getHexbinHeight(mapRows, hexRadius)) / 2 + hexRadius;
    const hexbin = d3Hexbin().radius(hexRadius);
    const translateX = adjustedOffSetX;
    const translateY = adjustedOffSetY;
    const hexbinPoints = hexbin(points);
    const textAreaHeight = hexRadius;
    const textAreaWidth = hexbinWidth * 0.9;
    let activeLabelFontSize = fontSize;
    let activeValueFontSize = fontSize;
    let isShowEllipses = false;
    let numOfChars = 0;

    if (fontAutoScale) {
      let maxLabel = '';
      let maxValue = '';
      for (let i = 0; i < data.length; i++) {
        if (data[i].name.length > maxLabel.length) {
          maxLabel = data[i].name;
        }
        if (_.toString(data[i].value).length > maxValue.length) {
          maxValue = _.toString(data[i].value);
        }
      }
      activeLabelFontSize = computeTextFontSize(maxLabel, 2, textAreaWidth, textAreaHeight);
      activeValueFontSize = computeTextFontSize(maxValue, 2, textAreaWidth, textAreaHeight);
      if (activeLabelFontSize < minFont) {
        isShowEllipses = true;
        numOfChars = 18;
        maxLabel = maxLabel.substring(0, numOfChars + 2);
        activeLabelFontSize = computeTextFontSize(maxLabel, 2, textAreaWidth, textAreaHeight);
        if (activeLabelFontSize < minFont) {
          numOfChars = 10;
          maxLabel = maxLabel.substring(0, numOfChars + 2);
          activeLabelFontSize = computeTextFontSize(maxLabel, 2, textAreaWidth, textAreaHeight);
          if (activeLabelFontSize < minFont) {
            numOfChars = 6;
            maxLabel = maxLabel.substring(0, numOfChars + 2);
            activeLabelFontSize = computeTextFontSize(maxLabel, 2, textAreaWidth, textAreaHeight);
          }
        }
      }
      // TODO: 暂时关闭序列名和值固定相同字体大小的设定
      // if (activeValueFontSize > activeLabelFontSize) {
      //   activeValueFontSize = activeLabelFontSize;
      // }
    }

    const valueWithLabelTextAlignment = textAreaHeight / 2 / 2;
    const labelWithValueTextAlignment = -(textAreaHeight / 2 / 2);

    svgGroup.attr('width', width).attr('height', height).attr('transform', `translate(${translateX},${translateY})`);

    const hexagons = svgGroup.selectAll('.hexagon').data(hexbinPoints);
    console.log('hexagons 选择器创建成功', { hexagonsCount: hexbinPoints.length });

    try {
      // 六边形
      console.log('创建 hexagonsEnter selection');
      const hexagonsEnter = hexagons
        .enter()
        .append(function () {
          const nodeToAdd = document.createElementNS(xmlns, 'path');
          return nodeToAdd;
        })
        .attr('class', 'hexagon')
        .on('mousemove', function (_d, i) {
          const metricObj = data[i]?.metric;
          const metricName = metricObj?.__name__ || 'value';
          const metricNameRow = `<div><strong>${metricName}: ${data[i]?.value}</strong></div>`;
          const labelsRows = _.map(_.omit(metricObj, '__name__'), (val, key) => {
            return `<div>${key}: ${val}</div>`;
          });
          const content = `${metricNameRow}${labelsRows.join('')}`;
          div.style('opacity', 0.9);
          div
            .html(content)
            .style('left', event.pageX + 10 + 'px')
            .style('top', event.pageY - 28 + 'px');

          const curPath = svgGroup.selectAll('.hexagon').nodes()[i];
          curPath.setAttribute('stroke', themeMode === 'dark' ? '#fff' : '#2A2D3C');
        })
        .on('mouseout', function (_d, i) {
          div.style('opacity', 0);
          const curPath = svgGroup.selectAll('.hexagon').nodes()[i];
          curPath.setAttribute('stroke', data[i]?.color);
        })
        .attr('stroke', (_d, i) => {
          return data[i]?.color;
        })
        .attr('stroke-width', '2px')
        .style('fill', (_d, i) => {
          return data[i]?.color;
        })
        .style('fill-opacity', 1);
      
      console.log('hexagons enter 选择器创建成功，准备应用 transition');
      
      try {
        // 完全重写 transition 部分，避免任何可能的问题
        console.log('开始应用 transition 到 hexagonsEnter');
        console.log('hexagonsEnter 类型:', typeof hexagonsEnter);
        
        // 记录更多信息以帮助调试
        if (hexagonsEnter) {
          console.log('hexagonsEnter 方法:', Object.keys(hexagonsEnter || {}).join(', '));
          console.log('hexagonsEnter 节点数量:', hexagonsEnter.size ? hexagonsEnter.size() : '未知');
        }
        
        try {
          // 方法1：直接设置属性，不使用 transition
          console.log('方法1：直接设置属性，不使用 transition');
          hexagonsEnter.attr('d', function (d) {
            return 'M' + d.x + ',' + d.y + hexbin.hexagon([hexRadius - 3]);
          });
          console.log('方法1 成功应用');
          
          // 方法2：如果需要动画效果，尝试使用 setTimeout 延迟应用样式
          setTimeout(() => {
            try {
              console.log('方法2：使用 setTimeout 延迟应用样式');
              // 重新选择元素，确保获取最新的 DOM
              const paths = svgGroup.selectAll('.hexagon');
              console.log('重新选择的 paths 数量:', paths.size());
              
              // 尝试应用 transition
              if (typeof paths.transition === 'function') {
                console.log('paths 有 transition 方法，尝试应用');
                paths
                  .transition()
                  .duration(750)
                  .style('opacity', 1);
                console.log('延迟 transition 应用成功');
              } else {
                console.log('paths 没有 transition 方法，跳过动画');
              }
            } catch (timeoutError) {
              console.error('setTimeout 中应用样式失败:', timeoutError);
            }
          }, 100);
        } catch (directError) {
          console.error('直接设置属性失败:', directError);
          console.error('错误类型:', directError.name);
          console.error('错误消息:', directError.message);
          console.error('错误堆栈:', directError.stack);
          
          // 回退方案：尝试使用原生 DOM API
          try {
            console.log('回退方案：使用原生 DOM API');
            const nodes = hexagonsEnter.nodes();
            console.log('获取到节点数量:', nodes ? nodes.length : 0);
            
            if (nodes && nodes.length > 0) {
              nodes.forEach((node, i) => {
                try {
                  const d = hexbinPoints[i];
                  if (d && node) {
                    const pathData = 'M' + d.x + ',' + d.y + hexbin.hexagon([hexRadius - 3]);
                    node.setAttribute('d', pathData);
                  }
                } catch (nodeError) {
                  console.error(`设置节点 ${i} 属性失败:`, nodeError);
                }
              });
              console.log('原生 DOM API 设置成功');
            } else {
              console.error('未获取到有效节点');
            }
          } catch (nativeError) {
            console.error('使用原生 DOM API 失败:', nativeError);
          }
        }
      } catch (error) {
        console.error('应用 transition 时发生错误:', error);
        console.error('错误类型:', error.name);
        console.error('错误消息:', error.message);
        console.error('错误堆栈:', error.stack);
        
        // 最终回退方案：尝试重新渲染整个图形
        try {
          console.log('最终回退方案：尝试重新渲染');
          svgGroup.selectAll('.hexagon').remove();
          
          svgGroup.selectAll('.hexagon')
            .data(hexbinPoints)
            .enter()
            .append('path')
            .attr('class', 'hexagon')
            .attr('d', function (d) {
              return 'M' + d.x + ',' + d.y + hexbin.hexagon([hexRadius - 3]);
            })
            .attr('stroke', (_d, i) => data[i]?.color)
            .attr('stroke-width', '2px')
            .style('fill', (_d, i) => data[i]?.color)
            .style('fill-opacity', 1);
          
          console.log('重新渲染成功');
        } catch (rerenderError) {
          console.error('重新渲染失败:', rerenderError);
        }
      }

      if (textMode === 'valueAndName' || textMode === 'name') {
        // 指标名
        try {
          console.log('开始创建文本元素 - 指标名');
          const nameTexts = hexagons
            .enter()
            .append('text')
            .attr('class', 'hexbin-name-text')
            .attr('x', function (d) {
              return d.x;
            })
            .attr('y', function (d) {
              return d.y + (textMode === 'valueAndName' ? labelWithValueTextAlignment : 0);
            })
            .text(function (_d, i) {
              let name = data[i]?.name;
              if (isShowEllipses) {
                name = name.substring(0, numOfChars) + '...';
              }
              return name;
            })
            .attr('text-anchor', 'middle')
            .attr('alignment-baseline', 'central')
            .style('pointer-events', 'none')
            .style('font-size', activeLabelFontSize + 'px')
            .style('fill', 'black');
          
          // 使用安全的方式设置 bbox
          nameTexts.each(function(d) {
            try {
              // 使用 d3.select 确保操作的是 D3 selection
              const element = d3.select(this);
              const bbox = this.getBBox();
              d.bbox = bbox;
            } catch (e) {
              console.error('获取文本 bbox 时出错:', e);
            }
          });
          
          console.log('指标名文本元素创建成功');
        } catch (error) {
          console.error('创建指标名文本元素时发生错误:', error);
        }
      }

      if (textMode === 'valueAndName' || textMode === 'value') {
        // 指标值
        try {
          console.log('开始创建文本元素 - 指标值');
          const valueTexts = hexagons
            .enter()
            .append('text')
            .attr('class', 'hexbin-value-text')
            .attr('x', function (d) {
              return d.x;
            })
            .attr('y', function (d) {
              return d.y + (textMode === 'valueAndName' ? valueWithLabelTextAlignment : 0);
            })
            .text(function (_d, i) {
              const value = data[i]?.value;
              return value;
            })
            .attr('text-anchor', 'middle')
            .attr('alignment-baseline', 'central')
            .style('font-size', activeValueFontSize + 'px')
            .style('fill', 'black')
            .style('pointer-events', 'none');
          
          // 使用安全的方式设置 bbox
          valueTexts.each(function(d) {
            try {
              // 使用 d3.select 确保操作的是 D3 selection
              const element = d3.select(this);
              const bbox = this.getBBox();
              d.bbox = bbox;
            } catch (e) {
              console.error('获取文本 bbox 时出错:', e);
            }
          });
          
          console.log('指标值文本元素创建成功');
        } catch (error) {
          console.error('创建指标值文本元素时发生错误:', error);
        }
      }
      console.log('renderHoneyComb: 蜂窝图渲染完成');
    } catch (error) {
      console.error('renderHoneyComb 渲染过程中发生错误:', error);
    }
  } catch (error) {
    console.error('renderHoneyComb 初始化过程中发生错误:', error);
  }
}

export function renderFn(data, { width, height, parentGroupEl, themeMode, textMode }) {
  console.log('renderFn: 开始渲染函数', { width, height, dataLength: data.length });
  try {
    const parentGroup = select(parentGroupEl).attr('width', width).attr('height', height);
    const countPerRow = bestFitElemCountPerRow(1, width, height);
    const unitWidth = Math.floor(width / countPerRow);
    const rowCount = Math.ceil(1 / countPerRow);
    const unitHeight = height / rowCount;

    renderHoneyComb(parentGroup, data, {
      width: unitWidth,
      height: unitHeight,
      themeMode,
      textMode,
    });
    console.log('renderFn: 渲染函数执行完成');
  } catch (error) {
    console.error('renderFn 执行过程中发生错误:', error);
  }
}
