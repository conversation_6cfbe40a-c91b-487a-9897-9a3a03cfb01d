.diagnostic-result {
  padding: 1.5rem;
  background-color: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.diagnostic-result h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 2rem;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 0.5rem;
}

.diagnostic-result h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.diagnostic-result h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.diagnostic-result p {
  margin-bottom: 1rem;
  line-height: 1.7;
  color: #4a5568;
}

.diagnostic-result ul,
.diagnostic-result ol {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.diagnostic-result li {
  margin-bottom: 0.5rem;
  line-height: 1.6;
  color: #4a5568;
}

.diagnostic-table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  background-color: #ffffff;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.diagnostic-table th {
  background-color: #f7fafc;
  color: #2d3748;
  font-weight: 600;
  padding: 1rem;
  text-align: left;
  border-bottom: 2px solid #e2e8f0;
}

.diagnostic-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e2e8f0;
  color: #4a5568;
  line-height: 1.5;
}

.diagnostic-table tr:nth-child(even) {
  background-color: #f8f9fa;
}

.diagnostic-table tr:hover {
  background-color: #e8f4fd;
  transition: background-color 0.2s;
}

.diagnostic-code-block {
  background-color: #2d3748;
  color: #e2e8f0;
  padding: 1.5rem;
  border-radius: 0.5rem;
  margin: 1.5rem 0;
  overflow-x: auto;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.diagnostic-code-block code {
  background: none;
  color: inherit;
  padding: 0;
  font-size: inherit;
}

.diagnostic-inline-code {
  background-color: #edf2f7;
  color: #2d3748;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.875rem;
  font-weight: 500;
}

.diagnostic-blockquote {
  border-left: 4px solid #4299e1;
  background-color: #ebf8ff;
  padding: 1rem 1.5rem;
  margin: 1.5rem 0;
  border-radius: 0 0.5rem 0.5rem 0;
  color: #2c5282;
  font-style: italic;
}

.diagnostic-hr {
  border: none;
  height: 2px;
  background: linear-gradient(to right, #4299e1, #63b3ed, #90cdf4);
  margin: 2rem 0;
  border-radius: 1px;
}

.diagnostic-result details {
  margin: 1.5rem 0;
  border-radius: 0.5rem;
  background-color: #f8f8f8;
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.diagnostic-result details summary {
  padding: 1rem;
  background-color: #f0f0f0;
  cursor: pointer;
  font-weight: 600;
  color: #2d3748;
  user-select: none;
  transition: background-color 0.2s;
}

.diagnostic-result details summary:hover {
  background-color: #e8e8e8;
}

.diagnostic-result details[open] summary {
  border-bottom: 1px solid #e2e8f0;
}

.diagnostic-result details > div {
  padding: 1rem;
  color: #4a5568;
  line-height: 1.6;
  background-color: #ffffff;
}

.diagnostic-result strong {
  color: #2d3748;
  font-weight: 600;
}

.diagnostic-result a {
  color: #4299e1;
  text-decoration: none;
  transition: color 0.2s;
}

.diagnostic-result a:hover {
  color: #2b6cb0;
  text-decoration: underline;
}

.ant-modal-content {
  border-radius: 0.75rem;
  overflow: hidden;
}

.ant-modal-header {
  background-color: #f7fafc;
  border-bottom: 1px solid #e2e8f0;
  padding: 1rem 1.5rem;
}

.ant-modal-body {
  padding: 1.5rem;
  max-height: 80vh;
  overflow-y: auto;
}

.ant-modal-title {
  color: #2d3748;
  font-weight: 600;
  font-size: 1.25rem;
}

.diagnostic-result ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.diagnostic-result ::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.diagnostic-result ::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.diagnostic-result ::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
