{"name": "n9e-fe", "version": "5.14.1", "scripts": {"dev": "vite --port 8765 --host", "dev:advanced": "vite --port 8765 --host --mode advanced", "build": "vite build", "build:advanced": "node comb_feats ${npm_config_custom_options} && vite build --mode advanced", "serve": "vite preview", "type-check": "tsc"}, "dependencies": {"@ant-design/charts": "^2.3.0", "@ant-design/icons": "^4.6.2", "@ant-design/plots": "^1.0.5", "@codemirror/autocomplete": "^0.19.0", "@codemirror/basic-setup": "^0.19.0", "@codemirror/highlight": "^0.19.0", "@codemirror/lang-json": "^0.19.1", "@codemirror/language": "^0.19.0", "@codemirror/lint": "^0.19.0", "@codemirror/state": "^0.19.0", "@codemirror/view": "^0.19.0", "@d3-charts/ts-graph": "^0.3.7", "@fc-plot/ts-graph": "^0.22.2", "@types/echarts": "^4.9.7", "@types/marked": "^6.0.0", "@uiw/react-codemirror": "4.6.0", "@y0c/react-datepicker": "^1.0.4", "ahooks": "3.1.5", "antd": "4.17.4", "array-move": "^4.0.0", "axios": "^1.9.0", "better-babel-generator": "^6.26.1", "classnames": "^2.3.1", "codemirror-promql": "^0.19.0", "color": "^4.0.1", "d3": "^4.10.0", "d3-hexbin": "^0.2.2", "dayjs": "^1.10.4", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "i18next": "20.3.2", "lezer": "^0.13.5", "lodash": "^4.17.21", "marked": "^15.0.11", "moment": "^2.29.1", "numeral": "^2.0.6", "query-string": "^7.0.1", "re-resizable": "^6.9.9", "react": "^17.0.0", "react-ace": "^8.1.0", "react-color": "^2.19.3", "react-dom": "^17.0.0", "react-fast-compare": "^3.2.0", "react-grid-layout": "^1.2.5", "react-highlight-words": "^0.17.0", "react-hooks-global-state": "^2.0.0", "react-i18next": "^11.11.1", "react-markdown": "^7.0.1", "react-redux": "^7.2.4", "react-router-dom": "^5.2.0", "react-saga": "^0.3.1", "react-sortable-hoc": "^1.8.3", "react-use": "^17.3.1", "redux-saga": "^1.1.3", "rehype-raw": "^6.1.1", "remark-gfm": "^1.0.0", "semver": "^7.3.5", "tslib": "^2.3.1", "umi-request": "^1.3.5", "uuid": "^8.3.2"}, "devDependencies": {"@babel/core": "^7.14.6", "@babel/parser": "^7.14.7", "@babel/runtime": "^7.26.10", "@commitlint/cli": "^12.1.1", "@commitlint/config-conventional": "^12.1.1", "@rollup/pluginutils": "^4.1.1", "@types/color": "^4.2.0", "@types/d3": "^7.4.3", "@types/echarts": "^4.9.7", "@types/jquery": "^3.5.5", "@types/lodash": "^4.17.16", "@types/node": "^15.0.1", "@types/react": "^17.0.14", "@types/react-dom": "^17.0.9", "@types/react-redux": "^7.1.34", "@types/react-router-dom": "^5.3.3", "@types/uuid": "^10.0.0", "@vitejs/plugin-react-refresh": "^1.3.1", "babel-generator": "^6.26.1", "babel-traverse": "^6.26.0", "babel-types": "^6.26.0", "better-babel-generator": "^6.26.1", "dotenv": "^16.0.1", "immutable": "^5.0.3", "less": "^4.1.1", "less-loader": "^8.1.1", "lint-staged": "^11.0.0", "postcss": "^8.5.3", "postcss-nested": "^5.0.5", "prettier": "^2.2.1", "prop-types": "^15.8.1", "rollup-plugin-visualizer": "^4.5.0", "tslint-config-prettier": "^1.18.0", "typescript": "4.9.5", "vite": "^2.1.5", "vite-plugin-react-svg": "^0.2.0", "webpack": "^5.98.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}